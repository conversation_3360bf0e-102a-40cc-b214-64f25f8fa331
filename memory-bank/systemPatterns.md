# System Patterns

*   **Architecture Overview:** Next.js App Router. Backend logic handled within Next.js using Server Components, Server Actions, and Route Handlers (no separate backend server). Supabase provides BaaS (Auth, DB, Storage, Realtime). The `src/` directory structure appears to be a hybrid approach, inspired by Feature-Sliced Design (FSD) but not strictly adhering to its layers (includes `app`, `widgets`, `features`, `components`, `hooks`, `lib`, `contexts`, `types`, `styles`).
*   **Key Technical Decisions:** 
    *   Using Next.js (v15+) with App Router for SSR, Server Actions, API Routes.
    *   Employing Supabase for backend services: Auth (Email/Pass, Social via `@supabase/ssr`), Database (PostgreSQL with RLS), Storage (Avatars), Realtime (Notifications).
    *   Using TypeScript for static typing.
    *   Styling with Tailwind CSS (v4) and Shadcn/ui components.
    *   State management primarily relies on Next.js server capabilities (Server Components, Actions). Client state managed with React Context (`src/contexts`) and potentially Zustand (mentioned in specs).
    *   Forms handled by React Hook Form with <PERSON>od for validation.
    *   Unit/Integration testing with Vitest.
    *   PWA functionality via `next-pwa`.
    *   Utilizing different Supabase clients based on context (Client Components, Server Components read-only, Server Actions/Route Handlers with cookies, Admin client for privileged server-side operations).
*   **Design Patterns:** 
    *   React Functional Components with Hooks.
    *   Component-based architecture.
    *   Server Components & Server Actions (Next.js).
    *   Context API for global state sharing (e.g., Theme).
    *   Utility-first CSS (Tailwind).
    *   Singleton pattern for Supabase client instances (`src/lib/supabase/client.ts`).
*   **Component Relationships:** Follows Next.js App Router conventions. FSD-inspired structure suggests vertical slicing by feature. Shared logic/components reside in `lib`, `components`, `hooks`, `contexts`. `app/` contains route definitions, layouts, Server Components. Server Actions often co-located or in `app/actions.ts`. Middleware (`middleware.ts`) handles Supabase session refreshing.
*   **Database Schema (Key Tables):**
    *   `profiles`: User details, linked to `auth.users`.
    *   `exercises`: Exercise definitions, criteria, medal thresholds.
    *   `submissions`: User performance video submissions, evaluation results.
    *   `notifications`: Realtime messages for users.
    *   `push_subscriptions`: Stores user PWA push notification endpoints.

*This document outlines the system architecture, key technical decisions, design patterns, and component relationships.* 
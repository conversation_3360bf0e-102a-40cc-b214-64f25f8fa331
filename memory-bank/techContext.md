# Technical Context

*   **Key Technologies:**
    *   Framework: Next.js (v15+)
    *   Language: TypeScript
    *   UI Library: React (v19)
    *   Styling: Tailwind CSS (v4), PostCSS, clsx, tailwind-merge, tailwindcss-animate. Fonts: <PERSON><PERSON><PERSON> (body), <PERSON> (headings). *Note: Potential color conflicts between styling.md and theme-system.md.*
    *   UI Components: Shadcn/ui, Radix UI primitives, lucide-react (icons)
    *   Backend/DB: Supabase (Auth, PostgreSQL DB, Storage, Realtime via `@supabase/ssr`, `@supabase/supabase-js`)
    *   Forms: Tanstack/react-from, Zod (validation)
    *   State Management: React Context, potentially Zustand.
    *   Testing: Vitest, React Testing Library, JSDOM.
    *   PWA: `next-pwa`, `web-push` (for push notifications), Lighthouse (for testing).
    *   Linting/Formatting: Biome, Markdownlint.
    *   Utilities: date-fns, cmdk, sonner (notifications), next-themes.
*   **Development Setup:** 
    1. Clone repository.
    2. Run `npm install`.
    3. Set up `.env.local` with Supabase URL/Anon Key (see `docs/supabase.md` for env var structure). Ensure `NEXT_PUBLIC_SUPABASE_ENV` is set (`local` or `production`).
    4. Run `npm run dev` (uses Turbopack).
*   **Technical Constraints:** PWA features require HTTPS in production and specific browser/OS support (e.g., iOS 16.4+ for push).
*   **Dependencies:** 
    *   Node.js (version per project requirements).
    *   Connection to a Supabase project.
    *   Generated VAPID keys (Public/Private) stored in env vars for production push notifications.
*   **Build/Deployment:** 
    *   Build: `npm run build`
    *   Start Production Server: `npm run start`
    *   Requires HTTPS setup for PWA functionality.
    *   Deployment Platform: [Not specified - e.g., Vercel, Netlify, Docker?]
*   **Supabase Clients:**
    *   Client Components: `createClient` from `@/lib/supabase/client`.
    *   Server Components (Read-Only): `createClient` from `@/lib/supabase/server`.
    *   Server Actions/Route Handlers: `getSupabaseRouteHandlerClient` from `@/lib/supabase/server` (uses `cookies`).
    *   Admin Operations (Server-Side): `createAdminClient` from `@/lib/supabase/server` (uses Service Role Key).
    *   Middleware: Uses `createServerClient` from `@supabase/ssr` for session handling.

*This document details the technologies used, development setup, constraints, and dependencies.* 
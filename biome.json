{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": []}, "formatter": {"enabled": true, "useEditorconfig": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 80, "attributePosition": "auto", "bracketSpacing": true}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true}}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "all", "semicolons": "asNeeded", "arrowParentheses": "always", "bracketSameLine": false, "quoteStyle": "single", "attributePosition": "auto", "bracketSpacing": true}}}
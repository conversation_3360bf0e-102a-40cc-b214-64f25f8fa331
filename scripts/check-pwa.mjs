#!/usr/bin/env node

/**
 * This script checks if the PWA setup is correct
 * Run with: node scripts/check-pwa.mjs
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, "..");
const publicDir = path.join(rootDir, "public");

const requiredFiles = [
  { path: "manifest.json", type: "file" },
  { path: "sw.js", type: "file" },
  { path: "android-chrome-192x192.png", type: "file" },
  { path: "android-chrome-512x512.png", type: "file" },
  { path: "apple-touch-icon.png", type: "file" },
];

const checkFileExists = (filePath, isDir = false) => {
  try {
    const fullPath = path.join(publicDir, filePath);
    const stats = fs.statSync(fullPath);
    if (isDir && !stats.isDirectory()) {
      return {
        exists: false,
        error: `${filePath} exists but is not a directory`,
      };
    }
    if (!isDir && !stats.isFile()) {
      return { exists: false, error: `${filePath} exists but is not a file` };
    }
    return { exists: true };
  } catch (error) {
    return {
      exists: false,
      error: `${filePath} does not exist: ${error.message}`,
    };
  }
};

const validateManifest = () => {
  try {
    const manifestPath = path.join(publicDir, "manifest.json");
    const manifest = JSON.parse(fs.readFileSync(manifestPath, "utf8"));

    const requiredFields = [
      "name",
      "short_name",
      "start_url",
      "display",
      "icons",
    ];
    const missingFields = requiredFields.filter((field) => !manifest[field]);

    if (missingFields.length > 0) {
      return {
        valid: false,
        error: `Manifest is missing required fields: ${missingFields.join(", ")}`,
      };
    }

    // Check if icons are properly configured
    if (!Array.isArray(manifest.icons) || manifest.icons.length === 0) {
      return {
        valid: false,
        error: "Manifest icons array is empty or not an array",
      };
    }

    // Check for maskable icon
    const hasMaskable = manifest.icons.some(
      (icon) => icon.purpose && icon.purpose.includes("maskable"),
    );

    return {
      valid: true,
      warnings: !hasMaskable ? ["No maskable icon found in manifest"] : [],
    };
  } catch (error) {
    return {
      valid: false,
      error: `Failed to validate manifest: ${error.message}`,
    };
  }
};

const validateServiceWorker = () => {
  try {
    const swPath = path.join(publicDir, "sw.js");
    const swContent = fs.readFileSync(swPath, "utf8");

    // Basic checks for common service worker events
    const hasInstallEvent = swContent.includes("install");
    const hasActivateEvent = swContent.includes("activate");
    const hasFetchEvent = swContent.includes("fetch");
    const hasSkipWaiting = swContent.includes("skipWaiting");
    const hasClientsClaim = swContent.includes("clientsClaim");

    const valid =
      hasInstallEvent ||
      hasActivateEvent ||
      hasFetchEvent ||
      (hasSkipWaiting && hasClientsClaim);

    const missingIndicators = [];
    if (!hasInstallEvent) missingIndicators.push("install");
    if (!hasActivateEvent) missingIndicators.push("activate");
    if (!hasFetchEvent) missingIndicators.push("fetch");
    if (!hasSkipWaiting) missingIndicators.push("skipWaiting");
    if (!hasClientsClaim) missingIndicators.push("clientsClaim");

    return {
      valid,
      warnings: valid
        ? missingIndicators.length > 0
          ? [`Service worker might be missing: ${missingIndicators.join(", ")}`]
          : []
        : [],
      error: valid
        ? undefined
        : "No recognizable service worker lifecycle or control methods found",
    };
  } catch (error) {
    return {
      valid: false,
      error: `Failed to validate service worker: ${error.message}`,
    };
  }
};

// Main validation function
const validatePWA = () => {
  console.log("🔍 Checking PWA configuration...\n");

  // Check required files
  console.log("📁 Checking required files:");
  let fileChecksPassed = true;

  for (const file of requiredFiles) {
    const result = checkFileExists(file.path, file.type === "directory");
    if (result.exists) {
      console.log(`✅ ${file.path}`);
    } else {
      console.log(`❌ ${file.path} - ${result.error}`);
      fileChecksPassed = false;
    }
  }

  // Validate manifest.json
  console.log("\n📄 Validating manifest.json:");
  const manifestResult = validateManifest();
  if (manifestResult.valid) {
    console.log("✅ Manifest is valid");

    if (manifestResult.warnings && manifestResult.warnings.length > 0) {
      for (const warning of manifestResult.warnings) {
        console.log(`⚠️ ${warning}`);
      }
    }
  } else {
    console.log(`❌ Manifest validation failed: ${manifestResult.error}`);
    fileChecksPassed = false;
  }

  // Validate service worker
  console.log("\n🔧 Validating service worker:");
  const swResult = validateServiceWorker();
  if (swResult.valid) {
    console.log("✅ Service worker looks good");

    if (swResult.warnings && swResult.warnings.length > 0) {
      for (const warning of swResult.warnings) {
        console.log(`⚠️ ${warning}`);
      }
    }
  } else {
    console.log(`❌ Service worker validation failed: ${swResult.error}`);
    fileChecksPassed = false;
  }

  // Final result
  console.log("\n📝 PWA Validation Summary:");
  if (fileChecksPassed && manifestResult.valid && swResult.valid) {
    console.log("✅ PWA configuration looks good!");
    console.log(
      "🚀 Your app should be installable on supported devices and browsers.",
    );
    console.log("\n💡 Testing tips:");
    console.log("1. Test in Chrome DevTools with Lighthouse");
    console.log("2. Test on actual mobile devices");
    console.log("3. Verify offline functionality");
    return true;
  } else {
    console.log("❌ PWA configuration has issues that need to be fixed.");
    console.log(
      "Please address the errors above to ensure your PWA works properly.",
    );
    return false;
  }
};

// Run the validation
validatePWA();

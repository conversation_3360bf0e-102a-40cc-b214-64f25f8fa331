#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to reset the Supabase database and apply migrations and seed data
 *
 * Usage: npx scripts/reset-supabase-db.mjs
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

// Function to execute shell commands
function executeCommand(command) {
  console.log(`\n> ${command}`);
  try {
    const output = execSync(command, { cwd: rootDir, stdio: 'inherit' });
    return output;
  } catch (err) {
    console.error(`Error executing command: ${command}`);
    console.error(err.message);
    process.exit(1);
  }
}

// Main function
async function main() {
  console.log(
    '🔄 Resetting Supabase database and applying migrations and seed data...'
  );

  // Check if Supabase CLI is installed
  try {
    execSync('supabase --version', { stdio: 'ignore' });
  } catch {
    console.error('❌ Supabase CLI is not installed. Please install it first:');
    console.error('npm install -g supabase');
    process.exit(1);
  }

  // Reset the database
  console.log('\n🗑️  Resetting the database...');
  executeCommand('supabase db reset');

  // Apply migrations
  console.log('\n⬆️  Applying migrations...');
  const migrationsDir = path.join(rootDir, 'supabase', 'migrations');
  const migrationFiles = fs.readdirSync(migrationsDir).sort();

  if (migrationFiles.length === 0) {
    console.log('No migration files found.');
  } else {
    for (const migrationFile of migrationFiles) {
      if (migrationFile.endsWith('.sql')) {
        console.log(`Applying migration: ${migrationFile}`);
        const migrationPath = path.join(migrationsDir, migrationFile);
        executeCommand(`supabase db execute --file ${migrationPath}`);
      }
    }
  }

  // Apply seed data
  console.log('\n🌱 Applying seed data...');
  const seedDir = path.join(rootDir, 'supabase', 'seed');
  const seedFiles = fs.readdirSync(seedDir).sort();

  if (seedFiles.length === 0) {
    console.log('No seed files found.');
  } else {
    for (const seedFile of seedFiles) {
      if (seedFile.endsWith('.sql')) {
        console.log(`Applying seed data: ${seedFile}`);
        const seedPath = path.join(seedDir, seedFile);
        executeCommand(`supabase db execute --file ${seedPath}`);
      }
    }
  }

  console.log('\n✅ Database reset and migrations applied successfully!');
  console.log('\n📊 Database schema:');
  executeCommand('supabase db dump --schema-only');
}

// Run the main function
main().catch((error) => {
  console.error('❌ An error occurred:', error);
  process.exit(1);
});

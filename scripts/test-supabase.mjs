/**
 * This script tests the connection to the local Supabase instance.
 * Run it with: node scripts/test-supabase.mjs
 */

// Load environment variables from .env.local
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Configure dotenv to load from .env.local
dotenv.config({ path: '.env.local' });

// Create a Supabase client using environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables!');
  console.error(
    'Make sure you have NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY in your .env.local file'
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testSupabaseConnection() {
  console.log('Testing connection to Supabase...');
  console.log(`URL: ${supabaseUrl}`);

  try {
    // Test the connection by checking the auth API
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      throw error;
    }

    console.log('✅ Successfully connected to Supabase!');
    console.log('✅ Authentication system is working!');
    console.log('Session:', data.session ? 'Active' : 'None');

    return true;
  } catch (error) {
    console.error('❌ Failed to connect to Supabase:', error);
    console.log('\nTroubleshooting tips:');
    console.log(
      '1. Make sure your local Supabase instance is running (supabase start)'
    );
    console.log(
      '2. Check that your .env.local file has the correct URL and keys'
    );
    console.log(
      '3. Verify that the Supabase URL is accessible in your browser'
    );
    console.log(
      '4. Try restarting your Supabase instance (supabase stop && supabase start)'
    );

    return false;
  }
}

// Run the test
testSupabaseConnection()
  .then((success) => {
    if (!success) {
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });

# Authentication System – Unified Implementation Guide

This document consolidates all authentication-related documentation for the project, ensuring consistency with the critical rules in `.clinerules/nextjs_supabase.md`. It covers SSR-safe Supabase Auth, UI modal flows, context usage, and best practices.

---

## 7. Role-Based Access Control (RBAC)

### Route-Level RBAC (ACL)

- All route access is controlled by a central Access Control List (ACL) in `src/lib/permissions/acl.ts`:

| Role        | Allowed Routes                                                     |
| ----------- | ------------------------------------------------------------------ |
| admin       | /, /admin, /submissions, /exercises, /rankings, /profile, /account |
| grandmaster | /, /submissions, /exercises, /rankings, /profile, /account         |
| athlete     | /, /exercises, /rankings, /profile, /account                       |

```ts
export const roleAccessMap = {
  admin: [
    "/",
    "/admin",
    "/submissions",
    "/exercises",
    "/rankings",
    "/profile",
    "/account",
  ],
  grandmaster: [
    "/",
    "/submissions",
    "/exercises",
    "/rankings",
    "/profile",
    "/account",
  ],
  athlete: ["/", "/exercises", "/rankings", "/profile", "/account"],
};
```

- The helper `isPathAllowed(role, path)` checks if a user with a given role can access a route.
- The middleware (`src/middleware.ts`) fetches the user's role after authentication and redirects to `/` if the route is not allowed for their role.
- The user's role is also passed as a header (`x-user-role`) for downstream use.

### In-Page Fine-Grained Permissions

- In-page actions (edit/remove) are controlled by a page-action matrix in `src/lib/permissions/pageRoles.ts`:

| Role        | Page         | edit | remove |
| ----------- | ------------ | ---- | ------ |
| admin       | /profile     | 1    | 1      |
| admin       | /submissions | 1    | 1      |
| admin       | /exercises   | 1    | 1      |
| grandmaster | /profile     | 1    | 0      |
| grandmaster | /submissions | 1    | 1      |
| grandmaster | /exercises   | 1    | 0      |
| athlete     | /profile     | 1    | 0      |
| athlete     | /submissions | 0    | 0      |
| athlete     | /exercises   | 0    | 0      |

```ts
export const pagePermissions = {
  admin: {
    "/profile": { edit: 1, remove: 1 },
    "/submissions": { edit: 1, remove: 1 },
    "/exercises": { edit: 1, remove: 1 },
  },
  grandmaster: {
    "/profile": { edit: 1, remove: 0 },
    "/submissions": { edit: 1, remove: 1 },
    "/exercises": { edit: 1, remove: 0 },
  },
  athlete: {
    "/profile": { edit: 1, remove: 0 },
    "/submissions": { edit: 0, remove: 0 },
    "/exercises": { edit: 0, remove: 0 },
  },
};
```

- The hook `useRoleAccess({ page, role })` returns `{ editLevel, removeLevel }` for the current user and page, defaulting to 0 if not allowed.
- Example usage:
  ```tsx
  const { editLevel, removeLevel } = useRoleAccess();
  if (editLevel) {
    /* show edit button */
  }
  if (removeLevel) {
    /* show remove button */
  }
  ```

### Server-Side RBAC Helpers

- Use `assertRole(["admin"])` or `assertPathAllowed("/admin")` in server actions/handlers to enforce RBAC.
- Example:
  ```ts
  import { assertRole } from "@/lib/permissions/rbac";
  export async function createExerciseAction(formData: FormData) {
    await assertRole(["admin"]);
    // ...action logic
  }
  ```

### Navbar and Layout

- The user's role is fetched server-side in `layout.tsx` and passed to the `Navbar` as a prop.
- The `Navbar` uses the role and ACL to conditionally render links (e.g., "Admin" for admins, "Submissions" for admins and grandmasters).

---

#### Developer Guideline

**Edit only the central ACL (`src/lib/permissions/acl.ts`) and page-action matrix (`src/lib/permissions/pageRoles.ts`) when changing role access. Do not scatter role logic elsewhere.**

---

### Testing

- Middleware and RBAC logic are tested with Vitest and React Testing Library.
- Tests cover:
  - Middleware redirection for unauthorized roles
  - Navbar conditional rendering
  - Server action RBAC enforcement

---

## 1. Supabase Auth SSR – Critical Rules

**You MUST follow these rules for all authentication code:**

- Use only `@supabase/ssr` for Supabase client creation.
- Use only `getAll` and `setAll` for cookie handling.
- Never use `get`, `set`, or `remove` on cookies.
- Never import from `@supabase/auth-helpers-nextjs`.

**Correct server client pattern:**

```typescript
import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";

export async function createClient() {
  const cookieStore = await cookies();
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options),
            );
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing user sessions.
          }
        },
      },
    },
  );
}
```

**Correct middleware pattern:**

```typescript
import { createServerClient } from "@supabase/ssr";
import { NextResponse, type NextRequest } from "next/server";

export async function middleware(request: NextRequest) {
  let supabaseResponse = NextResponse.next({ request });
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) =>
            request.cookies.set(name, value),
          );
          supabaseResponse = NextResponse.next({ request });
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options),
          );
        },
      },
    },
  );
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (
    !user &&
    !request.nextUrl.pathname.startsWith("/login") &&
    !request.nextUrl.pathname.startsWith("/auth")
  ) {
    const url = request.nextUrl.clone();
    url.pathname = "/login";
    return NextResponse.redirect(url);
  }
  return supabaseResponse;
}
```

---

## 2. Core Authentication Flows

### Server Actions

- Implemented in `src/features/auth/actions.ts`:
  - `signUp(email, password, redirectTo?)`
  - `signIn(email, password, redirectTo?)`
  - `signOut(redirectTo?)`
- All use SSR-safe Supabase client, handle errors, and redirect on success.

### Middleware

- Protects all `/app/*` and `/admin/*` routes (except static assets, `/login`, `/auth/*`, `/public/*`, `/`, `/exercises`).
- Redirects unauthenticated users to `/login`.
- Always calls `supabase.auth.getUser()` after client creation.

### OAuth/Email Callback

**Correct SSR-safe callback route:**

```typescript
// src/app/auth/callback/route.ts
import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get("code");

  if (code) {
    const cookieStore = await cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options),
              );
            } catch {
              // The `setAll` method was called from a Server Component.
            }
          },
        },
      },
    );
    await supabase.auth.exchangeCodeForSession(code);
  }
  return NextResponse.redirect(new URL("/profile", request.url));
}
```

---

## 3. Authentication Modal UI

### Features

- Single "Sign In" button triggers modal (no page redirects).
- Modal supports:
  - Email/password
  - Google, Facebook, Apple OAuth
  - Toggle between sign in and sign up
  - Form validation, error handling, loading states
- Modal is the only UI for sign-in/sign-up (no page-based forms).

### Key Implementation (Navbar Example)

**(Excerpt, see `src/components/Navbar.tsx` for full code):**

```tsx
import { createBrowserClient } from "@supabase/ssr";
const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
);

// Social auth handler
const handleSocialAuth = async (provider: "google" | "facebook" | "apple") => {
  setAuthLoading(true);
  setAuthError(null);
  const { error } = await supabase.auth.signInWithOAuth({
    provider,
    options: { redirectTo: `${window.location.origin}/auth/callback` },
  });
  if (error) setAuthError(error.message);
  setAuthLoading(false);
};

// Email/password handler
const handleEmailAuth = async (e: React.FormEvent) => {
  e.preventDefault();
  setAuthLoading(true);
  setAuthError(null);
  if (authMode === "signin") {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    if (error) setAuthError(error.message);
  } else {
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: { emailRedirectTo: `${window.location.origin}/auth/callback` },
    });
    if (error) setAuthError(error.message);
    else setAuthDialogOpen(false);
  }
  setAuthLoading(false);
};
```

### Integration

- Modal is integrated with profile navigation:
  - If user clicks "Profile" and is not authenticated, modal appears.
  - All old sign-in/sign-up pages are replaced by this modal.

---

## 4. Authentication Context & Usage Patterns

The authentication system is built around a centralized context provider.

### Usage

**Basic Authentication:**

```tsx
import { useAuth } from "@/contexts/AuthContext";
const { user, loading, signInWithEmail } = useAuth();
```

**Permissions:**

```tsx
import { usePermissions } from "@/hooks/usePermissions";
const { isAdmin } = usePermissions();
if (!isAdmin()) return null;
```

**Protected Routes:**

```tsx
import { ProtectedRoute } from "@/components/auth";
<ProtectedRoute requiredPermission="admin.access">
  <div>Admin Dashboard</div>
</ProtectedRoute>;
```

**Conditional UI:**

```tsx
import { AuthGuard } from "@/components/auth";
<AuthGuard requiredPermission="content.create">
  <button>Create Content</button>
</AuthGuard>;
```

### Flow

1. `AuthProvider` is initialized in the root layout.
2. Fetches current session and user profile.
3. Sets up listeners for auth state changes.
4. Components access auth state/methods via `useAuth`.
5. Permission checks via `usePermissions`.
6. Protected routes and conditional UI via `ProtectedRoute` and `AuthGuard`.

---

## 5. Testing & Next Steps

- **Manual E2E:** sign up, sign in, sign out, middleware protection, callback.
- **Integration:** server actions tested with Vitest (mock Supabase and next/navigation).
- **Component:** modal rendering and validation.

**Next Steps:**

- Optionally refactor modal to use server actions for SSR consistency.
- Expand integration and E2E test coverage.

---

## 6. Security Considerations

- Always use HTTPS for authentication.
- Implement CSP headers to prevent XSS.
- Consider rate limiting for authentication attempts.
- Validate all form inputs on both client and server.
- Double-check OAuth provider configuration.
- Never expose secrets or use deprecated cookie APIs.

---

**This document supersedes all previous authentication docs. All code and patterns must comply with the SSR and cookie handling rules above.**

# React 19 Migration Guide: useEffect to Server Actions

## Overview

This guide documents the migration from traditional `useEffect`-heavy patterns to React 19's modern server actions and simplified state management.

## Key Changes

### ✅ What We Migrated

1. **Authentication Forms** → Server Actions with `useActionState`
2. **Role-based Guards** → Simplified conditional rendering
3. **Complex State Management** → Built-in pending/error states

### ✅ What We Kept (Appropriate useEffect Usage)

1. **External System Subscriptions** (Supabase auth listeners)
2. **Browser API Integration** (intersection observers, geolocation)
3. **Cleanup Operations** (timers, event listeners)
4. **DOM Manipulation** (focus management)

## Migration Examples

### Before: Complex useEffect State Management

```typescript
// OLD PATTERN - Multiple useEffect hooks for state management
const [loading, setLoading] = useState(false);
const [error, setError] = useState<string | null>(null);
const [data, setData] = useState(null);

useEffect(() => {
  // Reset states when props change
  setError(null);
  setData(null);
}, [someProp]);

useEffect(() => {
  if (open) {
    setError(null);
  }
}, [open]);

const handleSubmit = async (formData) => {
  setLoading(true);
  setError(null);
  try {
    const result = await submitData(formData);
    if (!result.success) {
      setError(result.error);
    }
  } finally {
    setLoading(false);
  }
};
```

### After: React 19 useActionState

```typescript
// NEW PATTERN - Single useActionState hook
const [state, formAction, isPending] = useActionState(serverAction, null);

// Minimal useEffect only for necessary side effects
useEffect(() => {
  if (state?.success) {
    onSuccess?.();
  }
}, [state]);

return (
  <form action={formAction}>
    <input name="email" required />
    <button disabled={isPending}>{isPending ? 'Loading...' : 'Submit'}</button>
    {state?.error && <div>{state.error}</div>}
  </form>
);
```

## Implementation Steps

### 1. Server Actions (`src/app/auth/actions.ts`)

```typescript
'use server';

export async function signInAction(
  prevState: AuthActionState | null,
  formData: FormData
): Promise<AuthActionState> {
  const email = formData.get('email') as string;
  const password = formData.get('password') as string;

  // Server-side validation
  if (!email || !password) {
    return { error: 'Email and password required', success: false };
  }

  try {
    const supabase = createSupabaseServerClient();
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      return { error: error.message, success: false };
    }

    redirect('/dashboard'); // Server-side redirect
  } catch (error) {
    return { error: 'Unexpected error', success: false };
  }
}
```

### 2. Modern Form Component (`src/features/auth/components/ModernAuthForm.tsx`)

```typescript
export function ModernAuthForm({ mode = 'signin' }: Props) {
  const [authMode, setAuthMode] = useState(mode);

  // Replace complex state with useActionState
  const [signInState, signInAction, isSignInPending] = useActionState(
    signInAction,
    null
  );
  const [signUpState, signUpAction, isSignUpPending] = useActionState(
    signUpAction,
    null
  );

  const currentState = authMode === 'signin' ? signInState : signUpState;
  const currentAction = authMode === 'signin' ? signInAction : signUpAction;

  return (
    <form action={currentAction}>
      {/* Form automatically handles validation, errors, and pending states */}
    </form>
  );
}
```

### 3. Simplified Role Guard (`src/lib/auth/roleGuard.tsx`)

```typescript
// BEFORE - useEffect for redirects
useEffect(() => {
  if (!loading && (!user || !allowedRoles.includes(userRole))) {
    router.push(redirectTo);
  }
}, [user, userRole, loading, allowedRoles, redirectTo, router]);

// AFTER - Direct conditional rendering
export function RoleGuard({ allowedRoles, children, redirectTo }: Props) {
  const { user, userRole, loading } = useAuth();
  const router = useRouter();

  if (loading) return null;

  if (!user || !userRole || !allowedRoles.includes(userRole)) {
    router.push(redirectTo);
    return null;
  }

  return <>{children}</>;
}
```

## Benefits of React 19 Approach

### 🚀 Performance

- **Reduced re-renders**: Server actions don't trigger unnecessary re-renders
- **Smaller bundles**: Less client-side state management code
- **Better hydration**: Forms work before JavaScript loads

### 🔒 Security

- **Server-side validation**: Authentication logic runs on server
- **No sensitive operations** in client code
- **Built-in CSRF protection**

### 🎯 Developer Experience

- **Simpler state management**: One hook replaces multiple useState + useEffect
- **Built-in error handling**: Automatic error states and retry logic
- **Progressive enhancement**: Forms work without JavaScript

### ♿ Accessibility

- **Better form semantics**: Native form submission behavior
- **Improved error handling**: Screen reader friendly error states
- **Keyboard navigation**: Native form navigation

## When to Keep useEffect

### ✅ Appropriate useEffect Usage

```typescript
// External system subscriptions
useEffect(() => {
  const { data: authListener } = supabase.auth.onAuthStateChange(callback);
  return () => authListener.subscription.unsubscribe();
}, []);

// Browser API integration
useEffect(() => {
  const observer = new IntersectionObserver(callback);
  observer.observe(ref.current);
  return () => observer.disconnect();
}, []);

// DOM manipulation
useEffect(() => {
  inputRef.current?.focus();
}, []);
```

### ❌ Patterns to Migrate

```typescript
// Form state management → useActionState
useEffect(() => {
  if (formData.someField) {
    setError(null);
  }
}, [formData]);

// Loading states for async operations → useActionState
useEffect(() => {
  setLoading(true);
  submitData().finally(() => setLoading(false));
}, []);

// Route protection → conditional rendering
useEffect(() => {
  if (!user) router.push('/login');
}, [user]);
```

## Testing Strategy

### Server Actions Testing

```typescript
// Test server actions independently
import { signInAction } from '@/app/auth/actions';

test('signInAction validates email', async () => {
  const formData = new FormData();
  formData.append('email', 'invalid');
  formData.append('password', 'password');

  const result = await signInAction(null, formData);
  expect(result.error).toContain('valid email');
});
```

### Component Testing

```typescript
// Test components with server actions
import { render, screen } from '@testing-library/react';
import { ModernAuthForm } from './ModernAuthForm';

// Mock server actions
vi.mock('@/app/auth/actions', () => ({
  signInAction: vi.fn(),
  signUpAction: vi.fn(),
}));

test('form shows pending state', async () => {
  render(<ModernAuthForm />);
  // Form handles pending states automatically
});
```

## Migration Checklist

- [x] ✅ **Step 1**: Create server actions (`src/app/auth/actions.ts`)
- [x] ✅ **Step 2**: Update forms to use `useActionState`
- [x] ✅ **Step 3**: Simplify route guards (remove unnecessary `useEffect`)
- [x] ✅ **Step 4**: Create example modern form component
- [x] ✅ **Step 5**: Document migration strategy
- [x] ✅ **Step 6**: Create comprehensive tests for new server actions
- [x] ✅ **Step 7**: Remove old actions file and tests
- [x] ✅ **Step 8**: Complete AuthDialog migration to use new patterns

### ✅ Migration Complete!

The React 19 authentication migration is now complete. All authentication forms now use:

- **Server Actions** with `useActionState` for form handling
- **Progressive Enhancement** - forms work without JavaScript
- **Built-in Error Handling** - automatic error states and validation
- **Improved Security** - server-side validation and CSRF protection

### Next Steps (Optional Enhancements)

1. **Update other forms**: Apply `useActionState` to submission forms
2. **Add progressive enhancement**: Ensure all forms work without JavaScript
3. **Performance testing**: Measure bundle size and runtime improvements
4. **E2E testing**: Verify forms work in all scenarios

## Resources

- [React 19 Documentation](https://react.dev/blog/2024/12/05/react-19)
- [useActionState Hook](https://react.dev/reference/react/useActionState)
- [Server Actions Guide](https://react.dev/reference/rsc/server-functions)
- [Progressive Enhancement](https://react.dev/reference/react-dom/hooks/useFormStatus)

---

**Key Takeaway**: React 19 doesn't eliminate `useEffect`, but provides better alternatives for form handling and async operations. Use `useEffect` for external systems, keep server actions for data mutations, and embrace progressive enhancement for better UX.

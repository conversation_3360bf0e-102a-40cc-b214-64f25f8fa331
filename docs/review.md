Consolidated Code‑Review
Objective: cross‑check the two previous review documents, keep only items that refer to code that actually exists, and separate “future work” from genuine problems. Un‑implemented features are treated as neutral (neither bug nor violation).

──────────────────────────────────────────
1. Definitive Rule‑Violations (present in code)

Rule file              | Location(s) & Evidence | Why It’s a Violation
-----------------------|------------------------|---------------------
`security.mdc` – “never use dangerouslySetInnerHTML” | `src/app/pwa/page.tsx#L99` | XSS risk; rule explicitly forbids this API. **done**
`security.mdc` – “never commit secrets” | `.env.local` is in VCS | Environment file— even redacted— breaks rule; move to `.gitignore`. **done**
`sql_style.mdc` – “S<PERSON> lives in .sql/migrations, not in JS” | `src/lib/rankings.ts#L30‑48` raw SQL string | Must be extracted to migration / .sql helper. **done**
`fds.mdc` – “no TODOs / placeholders in production code” | ~52 `// TODO` comments (e.g. `src/lib/utils.ts#L14`) | Must be tracked in docs or resolved.
`react-ts.mdc` – “tests must accompany code” | No `*.test.*` anywhere while many items are checked off in docs/todo.md | Failing the testing‑required rule.

NOTE – The cookie‑handling pattern in `middleware.ts` *does* conform to `nextjs_supabase.mdc` (only `getAll`/`setAll` are used), so there is **no** violation of that critical rule.

──────────────────────────────────────────
2. Genuine Bugs / Defects in Existing Code

File & Line                          | Issue | Impact | Severity
-------------------------------------|-------|--------|---------
`middleware.ts#19‑23`                | `request.cookies.set(name, value)` is called without the `options` param (Path, SameSite, HttpOnly etc. are dropped). | Session cookies may lose security flags → auth instability. | High **done**
`middleware.ts#13`                   | First `NextResponse.next()` result stored in `supabaseResponse` is overwritten later; headers from earlier mutation risk being lost. | Could strip previously set headers / cookies. | Medium **done**
`src/lib/rankings.ts#34`             | Reads `process.env` inside a helper that might be imported client‑side. | Secrets could leak in a client bundle. | Medium **done**
`src/app/pwa/page.tsx#L99`           | `dangerouslySetInnerHTML` (see Rule‑Violation above) | Potential XSS. | Medium‑High **done**
`src/lib/supabase/credentials.ts`    | No memoisation; env look‑ups on every call. | Minor perf cost; not critical. | Low **done**

──────────────────────────────────────────
3. TODO Tracking Integrity

Finding | Action
--------|-------
Official checklist (`docs/todo.md`) and inline TODOs are not in sync. Examples: inline TODO for profile creation (`src/app/profile/create/page.tsx`) isn’t on the master list. | Adopt a **single source of truth**. Either remove inline TODOs after logging them into `docs/todo.md`, or mark them with an identifier linked to the doc.
“Checked‑off” tasks in docs/todo.md that are **not** implemented (e.g. public profile page, many tests). | Re‑audit progress and un‑check items until feature & tests are merged.

──────────────────────────────────────────
4. Code Duplication & Maintainability  **done**

Area | Observation | Recommendation
-----|-------------|---------------
Cookie handling logic | Similar blocks in `middleware.ts` and several util examples. | Extract to `src/lib/supabase/cookieHelpers.ts` and unit‑test once.
Supabase credential access | Repeated `getSupabaseCredentials()` / env reads. | Centralise and cache result.

──────────────────────────────────────────
5. Security Posture

✓  Uses `@supabase/ssr` correctly and obeys cookie API pattern.
✗  `dangerouslySetInnerHTML` and `.env.local` in repo breach internal security rules.
Next steps: sanitize / remove HTML injection, move env file out of VCS, review RLS policies once database scripts are added.

──────────────────────────────────────────
6. Testing & CI

Current state | Gap
--------------|----
Vitest config files exist. | Zero tests committed.
Workbox config exists. | No service‑worker registration test.

Add at least:
• Unit tests for cookie helper and middleware redirect logic.
• Lint + test run in CI (GitHub Actions).

──────────────────────────────────────────
7. Roadmap (features still to build – **not** bugs)

• Exercise CRUD, Submission flow, Evaluation, Ranking, Notifications, PWA offline, etc. (as per specification).
These are correctly tagged as future work; they do **not** count as current defects.

──────────────────────────────────────────
8. Immediate Action List (to move forward confidently)

Priority | Task
---------|-----
P0 | Patch `middleware.ts` to pass `options` into `request.cookies.set` and preserve first `supabaseResponse` headers.
P0 | Remove `.env.local` from repository, rotate keys if exposed.
P0 | Replace or sanitize `dangerouslySetInnerHTML` in `src/app/pwa/page.tsx`.
P1 | Extract raw SQL from `src/lib/rankings.ts` into migration / .sql file.
P1 | Re‑sync `docs/todo.md` with real progress; move all stray inline TODOs into it.
P1 | Add at least one Vitest proof‑of‑concept test (middleware).
P2 | Factor cookie helper and memoised credential util.
P2 | Establish CI pipeline running `eslint --max-warnings=0` and `vitest`.

By resolving the P0 and P1 items, the project will comply with internal rules, close obvious security holes, and set a solid base for implementing the remaining specification features.

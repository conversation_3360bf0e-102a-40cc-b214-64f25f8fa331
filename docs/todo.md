# Armwrestling Power Arena - TODO Checklist

This checklist follows the steps outlined in `docs/arm_power_arena_specs.md` (previously blueprint.md). Mark items as complete using `- [x]`. Remember to run tests and update relevant documentation after each step.

---

## Phase 1: Project Setup & Foundational Infrastructure

### Step 1.1: Initialize Next.js Project

- [x] Run `npx create-next-app` with TypeScript, Tailwind, ESLint, App Router, `src/` dir
- [x] Configure Biome
- [x] Clean default boilerplate code/styles
- [x] Create basic project directory structure (`src/app`, `src/lib`, `src/components`, etc.)
- [x] Create minimal root `layout.tsx` and `page.tsx`
- [x] Initialize Git repository (`git init`) and make initial commit
- [ ] Write/Run required Tests (dev server starts, base page renders, linters pass)
- [x] Update Documentation (`TODO.md` initial check, basic JSDoc)

### Step 1.2: Set Up ShadCN UI & Base Theme

- [x] Initialize ShadCN UI (`npx shadcn-ui@latest init`) targeting `src/shared/ui` (or similar location)
- [x] Install required ShadCN components (`button`, `card`, `input`, `label`, `select`, `textarea`, `dialog`, `avatar`, `dropdown-menu`, `badge`, `table`, `toast`/`sonner`)
- [x] Install and configure `next-themes`
- [x] Define theme in `tailwind.config.ts` based on spec color palette
- [x] Create theme toggle component
- [x] Implement basic typography (Google font for "modern, aggressive sports aesthetic")
- [x] Document theme setup in `docs/theme-system.md` (or similar)
- [x] Implement `ThemeProvider` in `layout.tsx` (dark default)
- [x] Configure `tailwind.config.ts` (colors, typography with `next/font`)
- [x] Define theme CSS variables in `src/styles/globals.css`
- [x] Create `ThemeToggle.tsx` component
- [x] Add `Toaster` component to `layout.tsx`
- [x] Write/Run required Tests (components render, theme toggle works, toaster present, responsiveness basic check, linters pass)
- [x] Update Documentation (`TODO.md`, theme setup docs, `ThemeToggle` docs)

### Step 1.3: Configure Supabase Client (@supabase/ssr)

- [x] Ensure local Supabase running (`npx supabase start`)
- [x] Install `@supabase/supabase-js` and `@supabase/ssr`
- [x] Create `.env.local` with Supabase keys
- [x] Add `.env.local` to `.gitignore`
- [x] Create `src/lib/supabase/client.ts` (Browser client utility)
- [x] Create `src/lib/supabase/server.ts` (Server context client utility)
- [x] Implement Supabase logic in root `middleware.ts` (using `createServerClient` from `@supabase/ssr`)
- [x] Write/Run required Tests (Unit tests for client utils creation, basic connectivity check)
- [x] Update Documentation (`TODO.md`, JSDoc for utils, README/docs on usage)

### Step 1.4: Set Up Basic Routing, Layout, and Navigation

- [x] Create route structure using folders (`app/auth`, `app/exercises`, `app/profile`, `app/admin`, etc.)
- [x] Update root `layout.tsx` with Header/Footer structure
- [x] Create `Navbar.tsx` (Client Component) with links (Home, Rankings, Exercises) and conditional Auth Button/User Menu
- [x] Include `Navbar` in `layout.tsx`
- [x] Create basic root `loading.tsx` and `error.tsx`
- [x] Create `(app)/layout.tsx` if needed, with potential `loading.tsx`/`error.tsx`
- [x] Write/Run required Tests (routes accessible, nav links work, loading/error states display correctly, linters pass)
- [x] Update Documentation (`TODO.md`, `docs/routing.md`, `Navbar` JSDoc)

### Step 1.5: Implement PWA Capabilities & Responsive Layout

- [x] Install `next-pwa`
- [x] Configure `next.config.js` with `withPWA`
- [x] Create `public/manifest.json`
- [x] Add icons to `public/icons/`
- [x] Update `layout.tsx` head with manifest link, theme-color, viewport meta tags
- [x] Review/Implement mobile-first responsive design in `layout.tsx`, `Navbar.tsx`, `page.tsx`
- [ ] Write/Run required Tests (Lighthouse PWA audit, responsive layout check across devices, install prompt)
- [x] Update Documentation (`TODO.md`, `docs/pwa-production-guide.md`, `docs/styling.md` note)

---

## Phase 2: Authentication & User Management

### Step 2.1: Create Supabase Database Schema (Users & Profiles)

- [x] Create migration file for `profiles` table
- [x] Define `profiles` table schema (FK to `auth.users`, `username` unique/lowercase, role, points, `last_submission_at`, etc.)
- [x] Add constraints (PK, FK, UNIQUE, CHECK)
- [x] Enable RLS on `profiles`
- [x] Define RLS Policies (public select, user insert/update self, admin manage)
- [x] (Optional) Create `handle_new_user` trigger function and trigger
- [x] Add indexes (`username`, `role`)
- [x] Write/Run required Tests (migration applies, schema inspect, RLS policies work via SQL, trigger works, seed script populates correctly)
- [x] Update Documentation (`TODO.md`, `docs/database-setup-guide.md` schema/RLS/trigger)

### Step 2.2: Implement Core Authentication Flows (@supabase/ssr)

- [x] Implement `signUp`, `signIn`, `signOut` Server Actions in `features/auth/actions.ts` (handle errors/redirects)
- [x] Implement `middleware.ts` using `@supabase/ssr` pattern (refresh session, protect routes)
- [x] Implement `/auth/callback/route.ts` handler (code exchange)
- [x] Unified authentication modal with social providers (Google, Facebook, Apple) is retained (no new page-based forms)
- [x] Write/Run required Tests (CRITICAL Manual E2E: signup, signin, signout, middleware protection, navbar state; Integration tests for actions; Basic component tests for modal)
- [x] Update Documentation (`TODO.md`, `docs/auth.md`, JSDoc actions/middleware, authentication tutorial)

### Step 2.3: Implement User Account Page & Public Profile Page (by Username)

- [x] Create `/account` page (Server Comp, protected)
- [x] Implement server fetch for logged-in user's profile on `/account`
- [x] Create `/profile/[username]` page (Server Comp, public)
- [x] Implement server fetch by `username` (lowercase) on `/profile/[username]` page
- [x] Handle `notFound()` for invalid usernames
- [x] Implement display logic for both pages (use ShadCN components)
- [x] Add placeholders for stats/history/achievements
- [x] Add links between `/account` and `/profile/[username]` / `/account/edit`
- [x] Write/Run required Tests (Access control, data display correct on both pages, username cas -insensitivity, not found handling)
- [x] Update Documentation (`TODO.md`, `docs/routing.md`, `docs/profile.md`, JSDoc pages)

### Step 2.4: Implement Basic Role-Based Access Control (RBAC)

- [x] Update `middleware.ts` to fetch user role and protect `/admin/*` routes
- [x] Add role checking logic template/example to Server Actions/Route Handlers
- [x] Update root `layout.tsx` to fetch role server-side
- [x] Update `Navbar.tsx` to accept role prop and conditionally render Admin/GM links
- [x] Write/Run required Tests (Middleware redirection based on role, UI conditional rendering in Navbar, simulated action auth check, ACL/page matrix/hook/RTL)
- [x] Update Documentation (`TODO.md`, `docs/auth.md`/`architecture.md` RBAC approach, JSDoc middleware)

### Step 2.5: Add Profile Editing Capabilities

- [x] Create 'avatars' bucket in Supabase Storage (public)
- [x] Define Storage RLS policies (public read, user manage own folder) via SQL/Studio
- [x] Create `/account/edit` page (Server Comp, protected)
- [x] Fetch current profile data server-side to pre-fill form
- [x] Create `EditProfileForm.tsx` (Client Comp, with validation, accessibility, responsive)
- [x] Implement client-side avatar upload logic in form (using browser client, upload to storage, get URL)
- [x] Implement `updateProfile` Server Action (auth check, username uniqueness check, update `profiles`)
- [x] Wire form `onSubmit` to call action, handle loading/feedback (toasts)
- [x] Write/Run required Tests (Storage RLS, form pre-fill, field updates, username uniqueness check, avatar upload/update, error handling)
- [x] Update Documentation (`TODO.md`, `docs/profile.md`, `docs/supabase.md` storage/RLS, JSDoc form/action)

---

## Phase 3: Exercise Library

### Step 3.1: Create Exercise Database Schema

- [x] Create migration file for `exercises` table
- [x] Define `exercises` table schema (`title`, `description`, `video_tutorial_url`, `equipment_required`, `evaluation_criteria`, `medal_thresholds` JSONB, `created_by` FK)
- [x] Add comments (esp. for `medal_thresholds` structure)
- [x] Enable RLS on `exercises`
- [x] Define RLS Policies (public select, admin manage)
- [x] Add indexes (`title`)
- [ ] Write/Run required Tests (migration applies, schema inspect, RLS policies work via SQL, `seed.sql` populates)
- [x] Update Documentation (`TODO.md`, `docs/database-setup-guide.md` schema/RLS)

### Step 3.2: Build Exercise Management for Admins (Modal Form)

- [x] Create `/admin/exercises` page (Server Comp, admin only check)
- [ ] Fetch exercises list server-side
- [ ] Create `ExerciseForm.tsx` (Client Comp, `@tanstack/react-form`)
- [ ] Create `ExerciseModalForm.tsx` (Client Comp, uses `Dialog`, embeds `ExerciseForm`)
- [ ] Implement `createExercise`, `updateExercise`, `deleteExercise` Server Actions (with admin check, data validation)
- [ ] Integrate modal triggers (Create/Edit buttons) on list page
- [ ] Wire form submission to actions, handle feedback/modal closing
- [ ] Implement delete confirmation (`AlertDialog`)
- [ ] Write/Run required Tests (Admin access, list display, Create/Update/Delete flows via UI, validation, admin checks)
- [x] Update Documentation (`TODO.md`, `docs/admin.md` exercise mgmt, JSDoc components/actions)

### Step 3.3: Develop Exercise Listing and Filtering (Public View)

- [x] Create `/exercises` page (Server Comp, public)
- [ ] Implement server fetch logic (read `searchParams` for search/filter/page, apply to query, get count)
- [ ] Create/Update `ExerciseCard.tsx` component
- [ ] Create `ExerciseFilters.tsx` (Client Comp, update URL params)
- [ ] Create/Update `PaginationControls.tsx` (Client Comp)
- [ ] Render Filters, Card Grid, Pagination on page
- [ ] Write/Run required Tests (Page load, search works, filters work, pagination works, empty state, responsiveness)
- [x] Update Documentation (`TODO.md`, `docs/features.md` listing/filtering, JSDoc components)

### Step 3.4: Create Detailed Exercise View Page

- [x] Create `/exercises/[id]` page (Server Comp, public)
- [x] Implement server fetch by `id`, handle `notFound()`
- [x] Create/Update `VideoPlayer.tsx` component (YouTube embed, fallbacks)
- [x] Create `GoToSubmitButton.tsx` (Client Comp, auth check, navigate to `/submit` with params)
- [x] Render exercise details, `VideoPlayer`, `GoToSubmitButton`
- [ ] Write/Run required Tests (Valid ID loads details, invalid ID shows 404, video player works, submit button visibility/navigation based on auth)
- [x] Update Documentation (`TODO.md`, `docs/features.md` detail view, JSDoc components)

---

## Phase 4: Video Submission System

### Step 4.1: Create Submissions Database Schema & RLS

- [x] Create migration file for `submissions` table
- [x] Define `submissions` table schema (FKs to `users`, `exercises`, `video_url`, `weight_kg`, `status`, `notes`, `evaluated_by`, `feedback`, `awarded_medal`, `points_awarded`)
- [x] Enable RLS on `submissions`
- [x] Define RLS Policies (user select/insert/update own, GM select all/update evaluation, admin manage)
- [x] Add indexes (`user_id`, `exercise_id`, `status`)
- [ ] Write/Run required Tests (migration applies, RLS policies work via SQL)
- [x] Update Documentation (`TODO.md`, `docs/database-setup-guide.md` schema/RLS)

### Step 4.2: Design Submission Form UI

- [x] Create `/submit` page (Server Comp, protected)
- [ ] Create `SubmissionForm.tsx` (Client Comp, `@tanstack/react-form`)
- [ ] Read exercise details from URL search params (`useSearchParams`)
- [ ] Implement form fields (Video URL, Weight, Notes) and validation (Zod schema)
- [ ] Implement basic video URL validation (client-side)
- [ ] Write/Run required Tests (Form renders, basic validation works)
- [x] Update Documentation (`TODO.md`, `docs/features.md` form, JSDoc form)

### Step 4.3: Implement Submission Logic (Server Action)

- [ ] Create `submitVideo` Server Action
- [ ] Add authentication check
- [ ] Add rate limiting (e.g., based on `profiles.last_submission_at`)
- [ ] Validate input data (exercise exists, user exists, URL format server-side)
- [ ] Insert submission record into `submissions` table (status 'pending')
- [ ] Update `profiles.last_submission_at`
- [ ] Handle errors and return success/failure feedback (toasts)
- [ ] Wire `SubmissionForm.tsx` `onSubmit` to call action
- [ ] Write/Run required Tests (Action success case, auth fail, rate limit fail, validation fails, DB insert correct)
- [x] Update Documentation (`TODO.md`, JSDoc action)

### Step 4.4: Display User Submission History

- [x] Create `/submissions/my` page (Server Comp, protected)
- [ ] Implement server fetch logic for logged-in user's submissions (with pagination)
- [ ] Create/Update `SubmissionCard.tsx` or `SubmissionRow.tsx` component
- [ ] Render submission list with status indicators, links to exercise/details
- [x] Add basic filtering/sorting options
- [ ] Write/Run required Tests (Page loads, submissions displayed correctly, pagination works)
- [x] Update Documentation (`TODO.md`, `docs/features.md` history, JSDoc page/component)

### Step 4.6: Set Up Database Seeding

- [x] Create `supabase/seed.sql`
- [x] Add idempotent `INSERT` statements for sample Exercises in `seed.sql`
- [x] Create `scripts/seed.ts`
- [x] Install `ts-node`, `dotenv` (and optionally `faker-js`) dev dependencies
- [x] Implement user/profile creation logic in `seed.ts` using Service Role Key
- [x] (Optional) Implement initial submission seeding in `seed.ts`
- [x] Add `db:seed` script to `package.json`
- [x] Add `SUPABASE_SERVICE_ROLE_KEY` to `.env` and `.gitignore`
- [ ] Write/Run required Tests (`npx supabase db reset` applies `seed.sql`, `npm run db:seed` creates users/profiles, idempotency check)
- [ ] Update Documentation (`TODO.md`, `docs/seeding.md`, README env vars)

---

## Phase 5: Evaluation & Medal System (GM/Admin)

### Step 5.1: Design Evaluation Interface

- [x] Create `/admin/submissions/pending` page (Server Comp, GM/Admin only)
- [ ] Fetch pending submissions list server-side

### Navbar and Authentication Improvements

- [x] Refactor Navbar component to follow Feature Sliced Design architecture
- [x] Extract authentication-related components from Navbar into separate files
- [x] Create a unified auth hook with role-based authorization
- [x] Create a component to handle Profile navigation that works with the auth dialog
- [x] Replace old sign-in pages with new auth modal for all auth-related flows
- [x] Update Profile page to work with the new auth flow
- [x] Ensure consistent authentication experience throughout the app

### Step 5.2: Implement Evaluation Logic (Server Action)

- [ ] Create `evaluateSubmission` Server Action
- [ ] Add GM/Admin auth check
- [ ] Validate input (submission exists and is pending, medal/points valid)
- [ ] Update `submissions` table (status, feedback, awarded medal, points, evaluated_by, evaluated_at)
- [ ] If approved, update `profiles` table (add points)
- [ ] (Optional) Trigger notification to user
- [ ] Handle errors, return feedback
- [ ] Wire `EvaluationForm.tsx` to call action
- [ ] Write/Run required Tests (Action success/fail cases, DB updates correct, profile points update)
- [x] Update Documentation (`TODO.md`, JSDoc action)

### Step 5.3: Display Medals and Points

- [x] Update `/profile/[username]` page to display total points and awarded medals list/summary
- [x] Update `/account` page similarly
- [x] Create migration for `medals` reference table (optional, if medal definitions are dynamic)
- [x] Create `MedalDisplay.tsx` component
- [ ] Write/Run required Tests (Points/medals display correctly on profiles)
- [x] Update Documentation (`TODO.md`, `docs/profile.md` medal display)

---

## Phase 6: Ranking System

### Step 6.1: Implement Ranking Logic

- [x] Create `rankings` database view or function (e.g., calculate ranks based on total points in `profiles`)
- [x] Consider periodic calculation/caching strategy if needed
- [x] Define logic for tie-breaking (e.g., fewer submissions, earlier last submission)
- [ ] Write/Run required Tests (SQL view/function produces correct ranks)
- [x] Update Documentation (`TODO.md`, `docs/database-setup-guide.md` ranking view/logic)

### Step 6.2: Build Rankings Page UI

- [x] Create `/rankings` page (Server Comp, public)
- [x] Fetch ranked users data from view/function (with pagination)
- [ ] Implement filtering controls (Client Components updating URL params): by exercise (requires joining submissions), maybe country/category later
- [ ] Create `RankingsTable.tsx` or similar component
- [ ] Display rank, username (link to profile), points, medals summary
- [ ] Implement pagination controls
- [ ] Write/Run required Tests (Page loads, rankings display correctly, pagination works, filtering works)
- [x] Update Documentation (`TODO.md`, `docs/features.md` rankings, JSDoc page/components)

---

## Phase 7: Admin Features

### Step 7.1: User Management

- [x] Create `/admin/users` page (Server Comp, admin only)
- [ ] Fetch user list server-side (with search/filter)
- [ ] Implement UI for viewing user details (`profiles` data)
- [ ] Create actions/UI for role assignment (Admin/GM/User)
- [ ] Implement user blocking/disabling functionality
- [ ] Write/Run required Tests (Page access, list loads, role change works, blocking works)
- [x] Update Documentation (`TODO.md`, `docs/admin.md` user mgmt)

### Step 7.2: Content Management (Exercises covered in Phase 3)

- [x] Review Exercise Management in Step 3.2

### Step 7.3: Site Configuration (Optional)

- [ ] Create `/admin/settings` page
- [ ] Implement UI for managing site-wide settings (e.g., announcement banners, feature flags) - Store in a dedicated `settings` table?
- [ ] Write/Run required Tests
- [ ] Update Documentation

---

## Phase 8: Testing, Deployment & Polish

### Step 8.1: Comprehensive Testing

- [ ] Write missing Unit tests for core components/utils
- [ ] Write missing Integration tests for Server Actions / Route Handlers
- [ ] Implement End-to-end tests (Playwright/Cypress) for critical user flows (Auth, Submit, Evaluate, Rank)
- [ ] Perform manual cross-browser/device testing
- [ ] Conduct accessibility audit (axe-core)

### Step 8.2: Performance & Optimization

- [ ] Analyze bundle size (`@next/bundle-analyzer`)
- [ ] Optimize images (`next/image`)
- [ ] Review database query performance (use `EXPLAIN ANALYZE`)
- [ ] Implement caching strategies where appropriate (React `cache`, Route Segment Config, external cache)
- [ ] Lighthouse performance audit

### Step 8.3: Deployment

- [ ] Set up CI/CD pipeline (GitHub Actions?) for linting, testing, building
- [ ] Configure production hosting environment (Vercel?)
- [ ] Set up production Supabase project
- [ ] Migrate database schema to production
- [ ] Configure environment variables for production
- [ ] Implement monitoring and logging (Vercel Analytics, Sentry?)

### Step 8.4: Documentation & Final Polish

- [ ] Review and update all documentation (`README.md`, `docs/*`)
- [ ] Add JSDoc/TSDoc comments to all major functions/components
- [ ] Ensure consistent UI/UX across the application
- [ ] Final code cleanup and refactoring
- [ ] Create user guide / help documentation (optional)

---

# Styling and Theme Guide

This document outlines the styling approach and theme configuration for the Armwrestling Power Arena application.

## Frameworks & Libraries

- **CSS Framework:** [Tailwind CSS](https://tailwindcss.com/)
- **UI Components:** [ShadCN UI](https://ui.shadcn.com/)
- **Theming:** [next-themes](https://github.com/pacocoursey/next-themes)

## Base Theme

The application uses a custom theme defined in `src/styles/globals.css` using CSS variables, following ShadCN conventions.

- **Default Mode:** Dark
- **Light Mode:** Supported
- **Theme Toggle:** Available in the Navbar.

### Color Palette

The theme is built upon the following core color palette, mapped to semantic CSS variables (e.g., `--primary`, `--secondary`, `--background`, `--foreground`):

- **<PERSON><PERSON> (`#53AE9B`):** Used for primary actions and accents (`--primary`, `--accent`, `--ring`).
- **<PERSON> Quartz (`#9a121d`):** Used for secondary elements (`--secondary`).
- **<PERSON> (`#54433E`):** Used for text on light backgrounds, dark backgrounds/cards, borders (`--foreground` (light), `--background` (dark), `--card` (dark), `--border`).
- **Ivory (`#FAFCF2`):** Used for text on dark backgrounds, light backgrounds/cards (`--foreground` (dark), `--background` (light), `--card` (light)).

Lighter/darker shades of these core colors are used for variations like muted text, input fields, etc.

### Medal Colors

Standard medal colors are defined as separate CSS variables:

- Gold: `--medal-gold` (#FFD700)
- Silver: `--medal-silver` (#C0C0C0)
- Bronze: `--medal-bronze` (#CD7F32)

These can be accessed via Tailwind utility classes like `bg-medal-gold`, `text-medal-silver`, etc. (after defining them in `tailwind.config.ts` which has been done).

## Typography

- **Body Font:** Geist Sans (imported via CSS `@import` from Google Fonts in `globals.css`). Applied via `font-sans` utility class.
- **Heading Font:** Oswald (imported via `next/font` in `layout.tsx`). Applied via `font-heading` utility class (defined in `tailwind.config.ts`).

## Component Styling

ShadCN UI components are used for the base UI elements. Customizations and layout are primarily handled using Tailwind CSS utility classes.

Component files are located in `src/components/ui/`.

## File Locations

- **Global Styles & Theme Variables:** `src/styles/globals.css`
- **Tailwind Configuration:** `tailwind.config.ts`
- **ShadCN UI Configuration:** `components.json`
- **Theme Provider Setup:** `src/app/layout.tsx`
- **Theme Toggle Component:** `src/features/theme/ThemeToggle.tsx` 
# Armwrestling Power Arena Theme System

This document outlines the theme system implemented for the Armwrestling Power Arena project using Tailwind CSS and ShadCN UI.

## Overview

The theme system provides a consistent design language across the application with:

- Dark mode as the default with light mode option
- Sports-themed color palette based on project specifications
- Responsive design for all device sizes
- Reusable UI components

## Color Palette

The color palette is based on the project specifications and implemented using CSS variables and Tailwind CSS:

### Primary Colors

- **Primary (CTA Red)**: `#E63946` - Used for primary actions and important UI elements
- **Secondary (Action Teal)**: `#2A9D8F` - Used for secondary actions and supporting UI elements

### Medal Colors

- **Gold**: `#FFD700` - For 1st place and gold medals
- **Silver**: `#C0C0C0` - For 2nd place and silver medals
- **Bronze**: `#CD7F32` - For 3rd place and bronze medals
- **Platinum**: `#E5E4E2` - For platinum medals
- **Diamond**: `#B9F2FF` - For diamond medals

### Background Colors

- **Dark Mode Background**: Rich black (`#121212`) with dark charcoal secondary panels (`#4d4646`)
- **Light Mode Background**: Off-white (`#F8F8F8`) with pure white panels (`#FFFFFF`)

### Accent Colors

- **Highlight Yellow**: `#FFBA08` - For important notifications or badges

### Text Colors

- **Dark Mode Text**: White (`#FFFFFF`) for headlines, light gray (`#E0E0E0`) for body text
- **Light Mode Text**: Dark gray (`#212121`) for headlines, medium gray (`#424242`) for body text

## Theme Configuration

The theme is configured using CSS variables in `globals.css` and the Tailwind configuration in `tailwind.config.ts`.

### CSS Variables

CSS variables are defined in `globals.css` using the `@layer base` directive:

```css
@layer base {
  :root {
    /* Light mode variables */
    --background: 0 0% 97%;
    --foreground: 0 0% 13%;
    /* ... other variables ... */
  }

  .dark {
    /* Dark mode variables */
    --background: 0 0% 7%;
    --foreground: 0 0% 93%;
    /* ... other variables ... */
  }
}
```

### Tailwind Configuration

The Tailwind configuration extends the default theme with custom colors, border radius, and animations:

```typescript
theme: {
  extend: {
    colors: {
      background: {
        DEFAULT: 'var(--background)',
        dark: '#121212',
        light: '#F8F8F8',
      },
      // ... other colors ...
    },
    // ... other extensions ...
  },
}
```

## Theme Provider

The theme system uses a React context provider (`ThemeProvider`) to manage theme state and provide theme-switching functionality:

```tsx
export function ThemeProvider({
  children,
  defaultTheme = 'dark',
}: ThemeProviderProps) {
  const [theme, setTheme] = useLocalStorage<Theme>('theme', defaultTheme);

  // ... theme management logic ...

  return (
    <ThemeProviderContext.Provider value={value}>
      {children}
    </ThemeProviderContext.Provider>
  );
}
```

## UI Components

The theme system includes several reusable UI components:

### Button

A versatile button component with multiple variants and sizes:

- **Variants**: default, destructive, outline, secondary, ghost, link
- **Sizes**: default, sm, lg, icon

### Card

A card component for displaying content in a contained, styled container:

- **CardHeader**: For card titles and descriptions
- **CardContent**: For the main content of the card
- **CardFooter**: For actions related to the card
- **CardTitle**: For the card's title
- **CardDescription**: For a brief description of the card's content

### Input

A styled input component for forms and user input.

### Navbar

A responsive navigation bar component with mobile menu support.

### ThemeToggle

A button component that toggles between light and dark mode.

## Usage

### Theme Toggle

```tsx
import { ThemeToggle } from '@/components/theme-toggle';

export default function MyComponent() {
  return (
    <div>
      <ThemeToggle />
    </div>
  );
}
```

### Button

```tsx
import { Button } from '@/components/Button';

export default function MyComponent() {
  return (
    <div>
      <Button>Default Button</Button>
      <Button variant="secondary">Secondary Button</Button>
      <Button variant="outline" size="lg">
        Large Outline Button
      </Button>
    </div>
  );
}
```

### Card

```tsx
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from '@/components/Card';
import { Button } from '@/components/Button';

export default function MyComponent() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Card Title</CardTitle>
        <CardDescription>Card Description</CardDescription>
      </CardHeader>
      <CardContent>
        <p>Card Content</p>
      </CardContent>
      <CardFooter>
        <Button>Action</Button>
      </CardFooter>
    </Card>
  );
}
```

## Best Practices

1. Use the provided color variables instead of hardcoding color values
2. Use the `cn()` utility function to combine Tailwind classes
3. Use the theme context to access the current theme
4. Use the provided UI components for consistency
5. Test both light and dark modes for all UI components

# Armwrestling Power Arena - Technical Specification

## 1. Project Overview

Armwrestling Power Arena is a platform where armwrestlers worldwide can showcase strength in specific exercises, get evaluated by experts, earn points/medals, and compete in global rankings.

### 1.1 Platform Type

- **Progressive Web App (PWA)**: Chosen for faster development, cross-platform accessibility, easier updates, offline capabilities, and device installation without app stores.

### 1.2 Core Features

- User authentication (Email/Password, Social) & profile management.
- Role-based access control (Athlete, <PERSON><PERSON>, Admin).
- Curated exercise library with details and video tutorials.
- Video submission system (YouTube, TikTok, Instagram links) with rate limiting.
- Expert evaluation system (Grandmasters/Admins) with private feedback.
- Points and medal allocation based on performance per exercise criteria.
- Realtime notification system (new submissions, evaluation results).
- Global ranking system with filtering (Gender, Weight Category, Country).
- Admin interface for user management (roles, blocking) and exercise management.
- Basic Admin analytics, content moderation, and system configuration (planned).

## 2. User Roles & Permissions

### 2.1 Athlete

- Register (Email/Password or Social) and create a basic profile (username required).
- Complete and edit their full profile (`full_name`, photo, country, gender, weight category, titles, social links).
- View exercise library, details, and video tutorials.
- Submit performance videos (via links) for specific exercises, adhering to rate limits (1 per 24h).
- View personal submission history with status, results (points/medals), and evaluator feedback.
- View personal achievements (total points, medal counts) on their account page.
- View global rankings and filter them.
- Receive notifications for evaluated submissions.

### 2.2 Grandmaster

- All Athlete permissions.
- Access the evaluation queue for pending submissions.
- Evaluate video submissions based on technique and exercise rules.
- Approve/Reject submissions.
- Award points and medals based on defined weight thresholds for approved submissions.
- Provide private feedback (`private_comments`) to athletes during evaluation.
- Receive notifications for new pending submissions.

### 2.3 Administrator

- All Grandmaster permissions.
- Access the admin dashboard/sections.
- Create, Read, Update, Delete exercises in the library.
- Manage users: View list, search/filter, change roles (Athlete, Grandmaster, Admin), block/unblock users.
- View submission history for any specific user.
- _(Planned Post-Core)_ View basic analytics dashboard.
- _(Planned Post-Core)_ Moderate content (e.g., remove inappropriate submissions).
- _(Planned Post-Core)_ Configure system parameters (e.g., medal thresholds, point values - Requires dedicated UI).

## 3. User Registration & Authentication

### 3.1 Registration Methods

- Email/Password registration (with potential email verification).
- Social media authentication - Google, Meta, Apple.

### 3.2 Profile Information

- **Required at Signup:** Email, Password, Unique Username (lowercase, specific format).
- **Managed Post-Signup (via `/account/edit`):**
  - Full Name (`full_name`)
  - Avatar/Profile Photo (`avatar_url` - uploaded to Supabase Storage)
  - Country
  - Gender (`male`, `female`)
  - Weight Category (`under_95`, `over_95`)
  - Armwrestling Titles (optional, array of strings)
  - Social Media Links (optional, JSON object: `{"youtube": "url", "instagram": "url", ...}`)
- **System Managed:**
  - Role (`athlete`- by default, `grandmaster`, `admin` - only assigned by other Admin)
  - Total Points (`total_points` - calculated from submissions)
  - Last Submission Timestamp (`last_submission_at` - for rate limiting)

## 4. Exercise System

### 4.1 Exercise Library (`/exercises`)

- Publicly viewable list of exercises.
- Filtering and searching capabilities.
- Exercises managed (CRUD) by Administrators via the admin interface.

### 4.2 Individual Exercise Page (`/exercises/[id]`)

- Displays detailed information for a single exercise:
  - Title and Description
  - Embedded Video Tutorial (`video_tutorial_url`)
  * Required Equipment (`equipment_required` - list)
  * Evaluation Criteria (`evaluation_criteria` - text)
  * Medal Thresholds (`medal_thresholds` - clearly displayed from JSONB)
- Includes a "Submit Performance" button for authenticated users, linking to `/submit` with exercise context.

### 4.3 Video Submission Process (`/submit`)

- Accessible via Exercise Page button or Account Page/Navbar link.
- If accessed from Exercise Page, exercise is pre-selected.
- If accessed from Account/Navbar, user selects exercise from a dropdown/search.
- User submits:
  - Link to video (YouTube, TikTok, Instagram). URL validation applied.
  - Weight Lifted (numeric, kg).
  - Optional Notes (text).
- **Rate Limiting:** Non-admin users limited to one submission per 24 hours.

## 5. Evaluation System

### 5.1 Submission Queue (`/admin/evaluate`)

- Accessible only by Grandmasters and Admins.
- Displays a list/table of submissions with `status = 'pending'`.
- Shows relevant info: Submitter, Exercise, Date, Video Link, Weight.
- Provides an "Evaluate" action for each submission, typically opening a modal.

### 5.2 Evaluation Process (within Modal)

- Evaluator reviews video, submitted weight, and exercise criteria.
- Evaluator decides to Approve or Reject.
- Evaluator provides mandatory/optional `private_comments`.
- **If Approved:**
  - System calculates `points_awarded` and `medal_awarded` using `calculateAward` function based on weight, user gender/weight category, and exercise `medal_thresholds`.
  - User's `total_points` in `profiles` is atomically incremented.
  - Submission status updated to 'approved', results saved.
- **If Rejected:**
  - Submission status updated to 'rejected', comments saved. Points/Medal remain 0/'none'.
- Submission timestamp (`evaluated_at`) and evaluator ID (`evaluated_by`) are recorded.

### 5.3 Medal & Points Logic (`calculateAward` function)

- Based _only_ on weight lifted, user gender, user weight category, and the `medal_thresholds` defined for the specific exercise.
- Thresholds (Example structure, actual values stored per exercise in DB):

  ```json
  // Example medal_thresholds JSON for one exercise
  {
    "women_under_95": {
      "bronze": 5,
      "silver": 15,
      "gold": 25,
      "platinum": 35,
      "diamond": 45
    },
    "women_over_95": {
      "bronze": 6,
      "silver": 16,
      "gold": 26,
      "platinum": 36,
      "diamond": 46
    }, // Assuming categories for women too
    "men_under_95": {
      "bronze": 20,
      "silver": 30,
      "gold": 40,
      "platinum": 50,
      "diamond": 60
    },
    "men_over_95": {
      "bronze": 25,
      "silver": 35,
      "gold": 45,
      "platinum": 60,
      "diamond": 70
    }
  }
  ```

- Point Values per Medal:
  - Bronze: 1 Point
  - Silver: 3 Points
  - Gold: 6 Points
  - Platinum: 10 Points
  - Diamond: 20 Points
  - None/Rejected: 0 Points

### 5.4 Feedback System

- `private_comments` from evaluation are stored with the submission.
- Athletes view these comments within their submission history on their `/account` page. Comments are not public.

## 6. Ranking System (`/rankings`)

### 6.1 Global Ranking Logic

- Primary Sort: Total Points (`total_points` from `profiles`) Descending.
- Tie-breaker: Medal Counts Descending (Diamond > Platinum > Gold > Silver > Bronze).
- Rank Calculation: `DENSE_RANK()` used to handle ties appropriately.
- Implementation: Via `ranked_users` Database View.

### 6.2 Filtering Options

- By Gender (`male`, `female`).
- By Weight Category (`under_95`, `over_95`).
- By Country (text search).

### 6.3 Ranking Display

- Table showing: Rank #, User (Avatar + Link to `/profile/[username]`), Country, Total Points, Medals (visual summary via `MedalsGallery`).
- Current logged-in user's row is highlighted.
- Pagination is implemented.
- No position change indicator in this version.

## 7. Technical Architecture

### 7.1 Frontend

- **Framework:** React / Next.js 14+ (App Router).
- **Styling:** Tailwind CSS.
- **UI Components:** ShadCN UI.
- **State Management:** React Context / Zustand (as needed, minimize complexity). Server state managed by Next.js caching/Server Actions/SWR/TanStack Query if complex client state arises.
- **Forms:** React Hook Form with Zod for validation.
- **PWA:** Implemented using `next-pwa` for installability and basic offline support.
- **Responsiveness:** Mobile-first design approach.

### 7.2 Backend & Logic

- **Platform:** Next.js App Router (Server Components, Server Actions, Route Handlers handle server-side logic).
- **BaaS:** Supabase utilized for:
  - Authentication (Email/Password, Social via built-in providers).
  - Database (PostgreSQL with Row Level Security).
  - Storage (Public bucket 'avatars' for user profile pictures).
  - Realtime (For `notifications` table).
- **No separate dedicated Node.js/Express backend server.**

### 7.3 Database Schema (Key Tables - See SQL in Plan for full details)

**`profiles`** (Linked to `auth.users`)
`id` (uuid, PK, FK to auth.users), `username` (text, unique), `full_name` (text), `avatar_url` (text), `country` (text), `gender` (enum), `weight_category` (enum), `titles` (text[]), `social_links` (jsonb), `total_points` (int), `role` (enum), `last_submission_at` (timestamptz), `created_at`, `updated_at`

**`exercises`**
`id` (bigint, PK), `title` (text, unique), `description` (text), `video_tutorial_url` (text), `equipment_required` (text[]), `evaluation_criteria` (text), `medal_thresholds` (jsonb), `created_by` (uuid, FK to profiles), `created_at`, `updated_at`

**`submissions`**
`id` (bigint, PK), `user_id` (uuid, FK to profiles), `exercise_id` (bigint, FK to exercises), `video_url` (text), `weight_lifted` (numeric), `notes` (text), `status` (enum), `submitted_at`, `evaluated_at`, `evaluated_by` (uuid, FK to profiles), `points_awarded` (int), `medal_awarded` (enum), `private_comments` (text)

**`notifications`**
`id` (bigint, PK), `recipient_user_id` (uuid, FK to profiles), `message` (text), `link_url` (text), `is_read` (boolean), `created_at`

_(No separate `Medals` table)_

## 8. UI/UX Design

### 8.1 Design Principles

- Modern, aggressive sports aesthetic (bold fonts, dynamic elements).
- Dark theme default, light theme option. Vibrant accent colors.
- High contrast for readability (WCAG AA minimum).
- Card-based layout for content.
- Mobile-first responsive design.

### 8.2 Color Palette

- **Primary Palette Definition (Conceptual Names & HEX):**
  - `rose_quartz`: #B7A9C1 (Likely for backgrounds/subtle elements)
  - `van_dyke`: #54433E (Dark tone, maybe for text on light bg, borders)
  - `ivory`: #FAFCF2 (Light background, text on dark bg)
  - `keppel`: #53AE9B (Primary Accent / Action Color - Teal-like)
- **Mapping to Usage (Example - Needs Refinement in Tailwind Config):**
  - **Backgrounds:** `ivory` (light), `van_dyke` / Darker Shade (dark)
  * **Text:** `van_dyke` (light theme), `ivory` (dark theme)
  * **Primary Accent / CTAs:** `keppel` (#53AE9B)
  * **Secondary Accent / Info:** `rose_quartz` (#B7A9C1)
  * **Borders / Panels:** Shades of `van_dyke` / `rose_quartz`
  * **Medal Colors:** Still use standard Gold (#FFD700), Silver (#C0C0C0), Bronze (#CD7F32) for medals specifically.
- **Tailwind Configuration:** These HEX codes will be mapped to semantic names (e.g., `primary`, `secondary`, `accent`, `background`, `foreground`, `card`, `input`, etc.) within `tailwind.config.ts` using CSS variables, following ShadCN conventions.

### 8.3 Key Screens (Content Outline)

- **Home Page / Landing:** Hero, Login/Register,Rankings Preview, Exercises, Medal System preview, Footer.
- **Exercise Library:** Filters/Search, Exercise Card Grid, Sorting.
- **Exercise Detail:** Video, Description, Criteria, Thresholds, Leaderboard (optional), Submit Button.
- **User Account (`/account`):** Profile Summary, Edit Link, Submit Link, Stats Placeholder, Submission History.
- **Profile Edit (`/account/edit`):** Form for editable fields, Avatar Upload.
- **Public Profile (`/profile/[username]`):** Public Info, Achievements (Points, Medals, Rank), Submission History Placeholder (or maybe public approved history later?).
- **Submission Form (`/submit`):** Exercise Selection (if needed), Video URL, Weight, Notes inputs.
- **Rankings Page (`/rankings`):** Leaderboard Table with sorting/filtering options, Pagination.
- **Admin Dashboard (`/admin`):** Links to sub-sections.
- **Admin Users (`/admin/users`):** User List, Filters, Role Edit actions, Block actions (planned).
- **Admin Exercises (`/admin/exercises`):** Exercise List, Create/Edit/Delete actions via Modal.
- **Admin Evaluate (`/admin/evaluate`):** Pending Submission Queue, Evaluate Modal.

## 9. Development Focus (Replacing Roadmap/Phases)

Development proceeds step-by-step following the `blueprint.md` plan. The focus is on delivering a high-quality, well-tested core experience first, including:

- Robust Auth & Profile Management.
- Functional Exercise Library (Admin managed).
- Smooth Video Submission & Evaluation flow.
- Accurate Ranking System.
- Clean, Responsive UI based on selected Design Principles/Palette.
- Core Admin functions (User/Exercise Management).
  Advanced features (detailed analytics, complex moderation, social aspects, system config UI) are planned but implemented after the core is stable and user feedback is gathered.

## 10. Testing Strategy

Integrated throughout development as per `blueprint.md`:

- **Unit Testing (Jest):** For utils, hooks, helpers.
- **Integration Testing (Jest):** For Server Actions, mocking DB/external calls.
- **Component Testing (Jest + RTL):** For UI component rendering and basic interaction.
- **End-to-End Testing (Playwright):** For critical user flows in a browser environment.
- **Manual Testing:** Crucial for verifying flows, especially Auth and Submission/Evaluation loops.

## 11. Security Considerations

Implemented throughout via:

- Supabase built-in Auth security.
- Strict Row Level Security (RLS) policies on database tables.
- Role checks in Middleware and Server Actions.
- Input validation (Client-side with Zod/React Hook Form, Server-side in actions).
- Using `@supabase/ssr` correctly for session management.
- Rate limiting on submissions.
- Standard Next.js security features (CSRF protection handled by framework for Server Actions).
- Secure handling of environment variables (especially Service Role Key).

## 12. Deployment Strategy

- **Local:** Next.js dev server (`npm run dev`), Local Supabase (`npx supabase start`).
- **Staging/Production:** Hosting via Vercel/Netlify (recommended for Next.js). Production Supabase project. CI/CD pipeline (e.g., GitHub Actions) for automated linting, testing, building, and deploying. Database migrations applied via Supabase CLI (`npx supabase migration up`).

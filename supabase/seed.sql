-- Seed file for initial exercises (idempotent)
-- This file is executed automatically by `supabase db reset` after migrations.
-- Inserts 3 sample exercises for development/testing. Uses ON CONFLICT (title) DO NOTHING for idempotency.

begin;

insert into public.exercises
  (title, description, evaluation_criteria, medal_thresholds, video_tutorial_url, equipment_required)
values
  (
    'Pronation Lift',
    'Static pronation hold off a loading pin. Focuses on wrist pronation strength.',
    'Max weight held for 5 seconds',
    '{"men_under_95":{"bronze":15,"silver":25,"gold":35},"men_over_95":{"bronze":20,"silver":30,"gold":40},"women_under_95":{"bronze":5,"silver":10,"gold":15}}',
    'https://www.youtube.com/watch?v=pronation_example',
    '{"Loading Pin","Strap"}'
  ),
  (
    'Cup Progression',
    'Progressive cupping exercise with a handle to build wrist flexion.',
    'Max weight for 8 reps, strict form',
    '{"men_under_95":{"bronze":20,"silver":30,"gold":40},"men_over_95":{"bronze":25,"silver":35,"gold":45},"women_under_95":{"bronze":7,"silver":12,"gold":18}}',
    'https://www.youtube.com/watch?v=cup_example',
    '{"Handle","Cable Machine"}'
  ),
  (
    'Wrist Roller',
    'Timed wrist roller for forearm endurance and grip.',
    'Fastest time to roll 10kg up 1 meter',
    '{"men_under_95":{"bronze":60,"silver":45,"gold":30},"men_over_95":{"bronze":55,"silver":40,"gold":28},"women_under_95":{"bronze":90,"silver":70,"gold":50}}',
    'https://www.youtube.com/watch?v=wristroller_example',
    '{"Wrist Roller","Weight Plate"}'
  )
on conflict (title) do nothing;

commit;

CREATE OR REPLACE FUNCTION public.get_ranked_profiles(
    _limit INT DEFAULT 50,
    _gender TEXT DEFAULT NULL,
    _weight_category TEXT DEFAULT NULL,
    _country TEXT DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    name TEXT,
    profile_image TEXT,
    country TEXT,
    gender TEXT,
    weight_category TEXT,
    role TEXT,
    total_points INT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.name,
        p.profile_image,
        p.country,
        p.gender,
        p.weight_category,
        p.role,
        p.total_points
    FROM
        public.profiles p
    WHERE
        (_gender IS NULL OR p.gender = _gender) AND
        (_weight_category IS NULL OR p.weight_category = _weight_category) AND
        (_country IS NULL OR p.country = _country)
    ORDER BY
        p.total_points DESC
    LIMIT _limit;
END;
$$; 
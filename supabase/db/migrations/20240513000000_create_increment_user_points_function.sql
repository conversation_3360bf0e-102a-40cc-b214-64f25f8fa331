-- Migration: Create increment_user_points function
-- Description: Creates a function to atomically increment a user's total points when a submission is approved
-- This function is used by the evaluateSubmission server action

-- Function to increment a user's total points
CREATE OR REPLACE FUNCTION increment_user_points(user_id_param UUID, points_param INTEGER)
RETURNS VOID AS $$
BEGIN
  -- Update the user's total points
  UPDATE profiles
  SET 
    total_points = total_points + points_param,
    updated_at = NOW()
  WHERE id = user_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

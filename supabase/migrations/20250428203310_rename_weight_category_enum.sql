-- Migration: Add new weight_category enum values 'under_95kg' and 'over_95kg'
-- Description: Adds new values to the public.weight_category enum.
-- Created At: 2025-04-28 20:33:10 UTC

-- 1. Add new values to the enum
alter type public.weight_category add value if not exists 'under_95kg';
alter type public.weight_category add value if not exists 'over_95kg';

-- Note: The subsequent UPDATE to use these values and the cleanup of old enum values
-- have been moved to separate, later migration files to avoid transaction errors.

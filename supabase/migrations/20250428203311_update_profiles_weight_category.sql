-- Migration: Update profiles to use new weight_category enum values
-- Description: Updates existing rows in the public.profiles table to use the 'under_95kg' and 'over_95kg' enum values added in the previous migration.
-- Depends On: 20250428203310_rename_weight_category_enum.sql
-- Created At: 2025-04-28 20:33:11 UTC

-- Update all rows in profiles to use new values
update public.profiles set weight_category = 'under_95kg' where weight_category = 'under_95';
update public.profiles set weight_category = 'over_95kg' where weight_category = 'over_95'; 
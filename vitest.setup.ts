// Global setup for Vitest (e.g., mocks, polyfills)
import "@testing-library/jest-dom/vitest"; // Import Jest DOM matchers
import { vi } from "vitest";

// --- Supabase GoTrueClient global mock ---
vi.mock("@/lib/supabase/server", async (importOriginal) => {
  const actual = (await importOriginal()) as Record<string, unknown>;
  return {
    ...actual,
    createClient: () => ({
      auth: {
        getUser: vi.fn().mockResolvedValue({ data: { user: null } }),
        // extend with more stubs when needed
      },
    }),
  };
});

// --- window.matchMedia mock for tests (for next-themes) ---
if (typeof window !== "undefined" && !window.matchMedia) {
  window.matchMedia = vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  }));
}

// Example: Mocking a global object if needed
// global.someApi = { mockMethod: vi.fn() };

// Add any global mocks or setup logic here

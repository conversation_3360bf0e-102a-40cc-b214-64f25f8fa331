'use client'
/**
 * AuthContext.tsx
 *
 * This file provides a centralized authentication context for the application.
 * It handles all authentication-related state and operations including:
 * - User authentication state
 * - User role and permissions
 * - Authentication methods (sign in, sign up, sign out)
 * - Profile data fetching
 */

import type { UserRole } from '@/lib/permissions/types'
import { getSupabaseCredentials } from '@/lib/supabase/credentials'
import {
  type EnhancedAuthResponse,
  getAuthErrorMessage,
  signInSchema,
  signUpSchema,
} from '@/lib/validation/auth'
import { createBrowserClient } from '@supabase/ssr'
import type { Session, User as SupabaseUser } from '@supabase/supabase-js'
import {
  type ReactNode,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react'

/**
 * User profile type with role information
 */
export interface UserProfile {
  id: string
  email: string
  role: UserRole
  displayName?: string
  avatarUrl?: string
  createdAt?: string
  // Use Record<string, unknown> instead of any for additional properties
  [key: string]: string | UserRole | undefined
}

/**
 * Auth context type definition
 */
export interface AuthContextType {
  // Authentication state
  user: SupabaseUser | null
  profile: UserProfile | null
  userRole: UserRole | null
  loading: boolean
  isAuthenticated: boolean

  // Core authentication methods with enhanced responses
  signInWithEmail: (
    email: string,
    password: string,
  ) => Promise<EnhancedAuthResponse>
  signUpWithEmail: (
    email: string,
    password: string,
  ) => Promise<EnhancedAuthResponse>
  signInWithSocial: (
    provider: 'google' | 'facebook' | 'apple',
  ) => Promise<EnhancedAuthResponse>
  signOut: () => Promise<void>

  // Password management
  resetPassword: (email: string) => Promise<EnhancedAuthResponse>
  updatePassword: (newPassword: string) => Promise<EnhancedAuthResponse>

  // Profile management
  updateProfile: (data: Partial<UserProfile>) => Promise<EnhancedAuthResponse>
  refreshProfile: () => Promise<void>
}

export type AuthContextValue = AuthContextType

// Create the auth context
export const AuthContext = createContext<AuthContextType | undefined>(undefined)

/**
 * Get the redirect URL safely from environment variables
 * This prevents open redirect vulnerabilities
 */
function getSecureRedirectUrl(): string {
  if (typeof window === 'undefined') {
    // Server-side: use environment variable
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || process.env.VERCEL_URL
    if (!baseUrl) {
      throw new Error('NEXT_PUBLIC_SITE_URL environment variable is required')
    }
    return `${baseUrl}/auth/callback`
  }

  // Client-side: use current origin (safe since we're already on the site)
  return `${window.location.origin}/auth/callback`
}

/**
 * AuthProvider component that wraps the application and provides authentication context
 */
export function AuthProvider({ children }: { children: ReactNode }) {
  // Authentication state
  const [user, setUser] = useState<SupabaseUser | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [userRole, setUserRole] = useState<UserRole | null>(null)
  const [loading, setLoading] = useState(true)

  // Create Supabase client
  const { supabaseUrl, supabaseAnonKey } = getSupabaseCredentials()
  const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey)

  /**
   * Fetches the user's profile from the database
   * @param userId The user's ID
   * @returns The user's profile or null if not found
   */
  const fetchUserProfile = useCallback(
    async (userId: string): Promise<UserProfile | null> => {
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .single()

        if (error) {
          console.error('Error fetching user profile:', error)
          return null
        }

        return data as UserProfile
      } catch (error) {
        console.error('Unexpected error fetching user profile:', error)
        return null
      }
    },
    [supabase],
  )

  /**
   * Refreshes the user's profile data
   */
  const refreshProfile = useCallback(async (): Promise<void> => {
    if (!user) return

    try {
      const userProfile = await fetchUserProfile(user.id)
      if (userProfile) {
        setProfile(userProfile)
        setUserRole(userProfile.role)
      }
    } catch (error) {
      console.error('Error refreshing profile:', error)
    }
  }, [user, fetchUserProfile])

  /**
   * Updates the user's profile in the database
   * @param data The profile data to update
   * @returns Success or error response
   */
  const updateProfile = useCallback(
    async (data: Partial<UserProfile>): Promise<EnhancedAuthResponse> => {
      if (!user) {
        return { success: false, error: 'User not authenticated' }
      }

      try {
        const { error } = await supabase
          .from('profiles')
          .update(data)
          .eq('id', user.id)

        if (error) throw error

        // Refresh the profile after update
        await refreshProfile()

        return { success: true }
      } catch (error) {
        console.error('Error updating profile:', error)
        const { message, type } = getAuthErrorMessage(error)
        return { success: false, error: message, errorType: type }
      }
    },
    [user, supabase, refreshProfile],
  )

  // Initialize auth state when component mounts
  useEffect(() => {
    const initializeAuth = async () => {
      setLoading(true)

      try {
        // Get the current session
        const {
          data: { session },
        } = await supabase.auth.getSession()
        setUser(session?.user || null)

        // If user is authenticated, fetch their profile
        if (session?.user) {
          const userProfile = await fetchUserProfile(session.user.id)
          setProfile(userProfile)
          setUserRole(userProfile?.role || null)
        } else {
          setProfile(null)
          setUserRole(null)
        }
      } catch (error) {
        console.error('Error initializing auth:', error)
      } finally {
        setLoading(false)
      }

      // Set up auth state change listener
      const { data: authListener } = supabase.auth.onAuthStateChange(
        async (event: string, session: Session | null) => {
          setUser(session?.user || null)

          if (session?.user) {
            const userProfile = await fetchUserProfile(session.user.id)
            setProfile(userProfile)
            setUserRole(userProfile?.role || null)
          } else {
            setProfile(null)
            setUserRole(null)
          }
        },
      )

      // Clean up listener on unmount
      return () => {
        authListener.subscription.unsubscribe()
      }
    }

    initializeAuth()
  }, [supabase.auth, fetchUserProfile])

  /**
   * Signs the user out
   */
  const signOut = async (): Promise<void> => {
    await supabase.auth.signOut()
  }

  /**
   * Signs in with a social provider
   * @param provider The social provider to use
   * @returns Success or error response
   */
  const signInWithSocial = async (
    provider: 'google' | 'facebook' | 'apple',
  ): Promise<EnhancedAuthResponse> => {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: provider,
        options: {
          redirectTo: getSecureRedirectUrl(),
        },
      })

      if (error) {
        const { message, type } = getAuthErrorMessage(error)
        return { success: false, error: message, errorType: type }
      }

      return { success: true }
    } catch (error) {
      console.error(`${provider} auth error:`, error)
      const { message, type } = getAuthErrorMessage(error)
      return { success: false, error: message, errorType: type }
    }
  }

  /**
   * Signs in with email and password
   * @param email The user's email
   * @param password The user's password
   * @returns Success or error response
   */
  const signInWithEmail = async (
    email: string,
    password: string,
  ): Promise<EnhancedAuthResponse> => {
    try {
      // Input validation
      const validation = signInSchema.safeParse({ email, password })
      if (!validation.success) {
        const firstError = validation.error.errors[0]
        return {
          success: false,
          error: firstError.message,
          errorType:
            firstError.path[0] === 'email'
              ? 'INVALID_EMAIL'
              : 'INVALID_CREDENTIALS',
        }
      }

      const { error } = await supabase.auth.signInWithPassword({
        email: validation.data.email,
        password: validation.data.password,
      })

      if (error) {
        const { message, type } = getAuthErrorMessage(error)
        return { success: false, error: message, errorType: type }
      }

      return { success: true }
    } catch (error) {
      console.error('Email sign in error:', error)
      const { message, type } = getAuthErrorMessage(error)
      return { success: false, error: message, errorType: type }
    }
  }

  /**
   * Signs up with email and password
   * @param email The user's email
   * @param password The user's password
   * @returns Success or error response
   */
  const signUpWithEmail = async (
    email: string,
    password: string,
  ): Promise<EnhancedAuthResponse> => {
    try {
      // Input validation
      const validation = signUpSchema.safeParse({ email, password })
      if (!validation.success) {
        const firstError = validation.error.errors[0]
        return {
          success: false,
          error: firstError.message,
          errorType:
            firstError.path[0] === 'email' ? 'INVALID_EMAIL' : 'WEAK_PASSWORD',
        }
      }

      const { error, data } = await supabase.auth.signUp({
        email: validation.data.email,
        password: validation.data.password,
        options: {
          emailRedirectTo: getSecureRedirectUrl(),
        },
      })

      if (error) {
        const { message, type } = getAuthErrorMessage(error)
        return { success: false, error: message, errorType: type }
      }

      // Check if email confirmation is required
      const needsConfirmation =
        !data.session && data.user && !data.user.email_confirmed_at

      if (needsConfirmation) {
        return {
          success: true,
          needsConfirmation: true,
          data: {
            message:
              'Please check your email and click the confirmation link to complete your registration.',
          },
        }
      }

      return { success: true }
    } catch (error) {
      console.error('Email sign up error:', error)
      const { message, type } = getAuthErrorMessage(error)
      return { success: false, error: message, errorType: type }
    }
  }

  /**
   * Sends a password reset email
   * @param email The user's email
   * @returns Success or error response
   */
  const resetPassword = async (
    email: string,
  ): Promise<EnhancedAuthResponse> => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${getSecureRedirectUrl().replace('/callback', '/update-password')}`,
      })

      if (error) {
        const { message, type } = getAuthErrorMessage(error)
        return { success: false, error: message, errorType: type }
      }

      return { success: true }
    } catch (error) {
      console.error('Reset password error:', error)
      const { message, type } = getAuthErrorMessage(error)
      return { success: false, error: message, errorType: type }
    }
  }

  /**
   * Updates the user's password
   * @param newPassword The new password
   * @returns Success or error response
   */
  const updatePassword = async (
    newPassword: string,
  ): Promise<EnhancedAuthResponse> => {
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      })

      if (error) {
        const { message, type } = getAuthErrorMessage(error)
        return { success: false, error: message, errorType: type }
      }

      return { success: true }
    } catch (error) {
      console.error('Update password error:', error)
      const { message, type } = getAuthErrorMessage(error)
      return { success: false, error: message, errorType: type }
    }
  }

  // Provide the auth context value
  const value: AuthContextType = {
    // Authentication state
    user,
    profile,
    userRole,
    loading,
    isAuthenticated: !!user,

    // Core authentication methods
    signInWithEmail,
    signUpWithEmail,
    signInWithSocial,
    signOut,

    // Password management
    resetPassword,
    updatePassword,

    // Profile management
    updateProfile,
    refreshProfile,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

/**
 * Hook to use the auth context
 * @returns The auth context
 * @throws Error if used outside of an AuthProvider
 */
export function useAuth() {
  const context = useContext(AuthContext)

  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }

  return context
}

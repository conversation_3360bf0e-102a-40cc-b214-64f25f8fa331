'use client'

import { Menu } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import type * as React from 'react'
import { useState } from 'react'

import { But<PERSON> } from '@/components/ui/button'
import { useAuth } from '@/contexts/AuthContext'
import { AuthDialog, UserMenu } from '@/features/auth/components'
import { ThemeToggle } from '@/features/theme/ThemeToggle'
import { cn } from '@/lib/utils'
import { MobileMenu, ProfileLink } from '@/widgets/navigation'
import { DEFAULT_NAVIGATION_ITEMS } from '@/widgets/navigation/constants'

import { roleAccessMap } from '@/lib/permissions/acl'

export interface NavbarProps
  extends Omit<React.HTMLAttributes<HTMLElement>, 'role'> {
  items?: {
    href: string
    title: string
  }[]
  role?: 'admin' | 'grandmaster' | 'athlete' | null
}

/**
 * Main navigation component with responsive design and auth state awareness
 * Displays different options based on user authentication status
 */
/**
 * Main navigation component with responsive design and auth state awareness
 * Displays different options based on user authentication status and role
 */
export function Navbar({
  className,
  items = DEFAULT_NAVIGATION_ITEMS,
  role,
  ...props
}: NavbarProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [authDialogOpen, setAuthDialogOpen] = useState(false)
  const [authDialogMode, setAuthDialogMode] = useState<'signin' | 'signup'>(
    'signin',
  )

  const { user, loading } = useAuth()

  const toggleMenu = () => {
    setIsOpen(!isOpen)
  }

  const handleAuthClick = (mode: 'signin' | 'signup') => {
    setAuthDialogMode(mode)
    setAuthDialogOpen(true)
  }

  // Build nav items based on role and ACL
  let displayItems = items
  if (role) {
    // Only show items whose href is allowed for this role
    displayItems = items.filter((item) =>
      roleAccessMap[role].some((prefix) =>
        prefix === '/' ? item.href === '/' : item.href.startsWith(prefix),
      ),
    )
    // Add Admin link for admin
    if (role === 'admin' && !displayItems.some((i) => i.href === '/admin')) {
      displayItems = [...displayItems, { href: '/admin', title: 'Admin' }]
    }
    // Add Submissions link for admin and grandmaster
    if (
      (role === 'admin' || role === 'grandmaster') &&
      !displayItems.some((i) => i.href === '/submissions')
    ) {
      displayItems = [
        ...displayItems,
        { href: '/submissions', title: 'Submissions' },
      ]
    }
  }

  return (
    <>
      <nav
        className={cn(
          'fixed top-0 left-0 z-40 w-full border-b border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-900',
          className,
        )}
        {...props}
      >
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            {/* Logo and brand */}
            <div className="flex items-center">
              <Link href="/" className="flex items-center space-x-2">
                <Image
                  src="/images/logo.webp"
                  alt="Armwrestling Power Arena Logo"
                  width={40}
                  height={40}
                  className="h-10 w-auto"
                />
                <span className="hidden text-lg font-bold text-[#9a121d] sm:inline">
                  Armwrestling Power Arena
                </span>
              </Link>
            </div>

            {/* Desktop navigation */}
            <div className="hidden md:block">
              <div className="ml-10 flex items-center space-x-4">
                {displayItems.map((item) => {
                  // Special handling for profile link
                  if (item.href === '/profile') {
                    return (
                      <ProfileLink
                        key={item.href}
                        onAuthClick={handleAuthClick}
                        className="rounded-md px-3 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-gray-100"
                      >
                        {item.title}
                      </ProfileLink>
                    )
                  }

                  // Regular links for other items
                  return (
                    <Link
                      key={item.href}
                      href={item.href}
                      className="rounded-md px-3 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-gray-100"
                    >
                      {item.title}
                    </Link>
                  )
                })}
              </div>
            </div>

            {/* Right side items */}
            <div className="flex items-center space-x-4">
              {loading ? (
                <div className="h-8 w-8 animate-pulse rounded-full bg-gray-200 dark:bg-gray-700" />
              ) : user ? (
                <UserMenu />
              ) : (
                <div className="hidden items-center md:flex">
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => handleAuthClick('signin')}
                  >
                    Sign In
                  </Button>
                </div>
              )}

              <ThemeToggle />

              {/* Mobile menu button */}
              <div className="md:hidden">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleMenu}
                  aria-label="Toggle menu"
                >
                  <Menu className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile navigation */}
        {isOpen && (
          <MobileMenu
            navigationItems={items}
            onAuthClick={handleAuthClick}
            onClose={() => setIsOpen(false)}
          />
        )}
      </nav>

      {/* Auth Dialog */}
      <AuthDialog
        open={authDialogOpen}
        onOpenChange={setAuthDialogOpen}
        initialMode={authDialogMode}
      />
    </>
  )
}

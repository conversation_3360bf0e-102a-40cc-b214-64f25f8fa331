import { Button } from '@/components/ui/button'
import { useAuth } from '@/contexts/AuthContext'
import Link from 'next/link'

interface MobileMenuProps {
  navigationItems: Array<{ href: string; title: string }>
  onAuthClick: (mode: 'signin' | 'signup') => void
  onClose: () => void
}

export function MobileMenu({
  navigationItems,
  onAuthClick,
  onClose,
}: MobileMenuProps) {
  const { user, signOut, isAuthenticated } = useAuth()
  return (
    <div className="md:hidden">
      <div className="space-y-1 px-2 pt-2 pb-3">
        {navigationItems.map((item) => {
          // Special handling for profile link when not signed in
          if (item.href === '/profile' && !user) {
            return (
              <button
                key={item.href}
                className="block w-full rounded-md px-3 py-2 text-left text-base font-medium text-gray-700 transition-colors hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-gray-100"
                onClick={() => {
                  onAuthClick('signin')
                  onClose()
                }}
              >
                {item.title}
              </button>
            )
          }

          // Regular links for all other items or profile when signed in
          return (
            <Link
              key={item.href}
              href={item.href}
              className="block rounded-md px-3 py-2 text-base font-medium text-gray-700 transition-colors hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-gray-100"
              onClick={onClose}
            >
              {item.title}
            </Link>
          )
        })}

        {user ? (
          <div className="mt-4 flex flex-col space-y-2 border-t border-gray-200 pt-4 dark:border-gray-700">
            <Link href="/account" onClick={onClose}>
              <Button variant="ghost" className="w-full justify-start">
                My Account
              </Button>
            </Link>
            <Link href="/submit" onClick={onClose}>
              <Button variant="ghost" className="w-full justify-start">
                Submit Performance
              </Button>
            </Link>
            <Link href="/account/submissions" onClick={onClose}>
              <Button variant="ghost" className="w-full justify-start">
                My Submissions
              </Button>
            </Link>
            <Button
              variant="destructive"
              className="w-full justify-start"
              onClick={async () => {
                await signOut()
                onClose()
              }}
            >
              Log Out
            </Button>
          </div>
        ) : (
          <div className="mt-4 flex flex-col space-y-2 border-t border-gray-200 pt-4 dark:border-gray-700">
            <Button
              variant="default"
              className="w-full justify-start"
              onClick={() => {
                onAuthClick('signin')
                onClose()
              }}
            >
              Sign In
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

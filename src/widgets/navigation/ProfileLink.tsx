'use client'

import { useAuth } from '@/contexts/AuthContext'
import Link from 'next/link'
import { ReactNode } from 'react'

interface ProfileLinkProps {
  children: ReactNode
  className?: string
  onAuthClick: (mode: 'signin' | 'signup') => void
}

/**
 * Component that either navigates to the profile page if signed in or opens the auth dialog if not
 */
export function ProfileLink({
  children,
  className,
  onAuthClick,
}: ProfileLinkProps) {
  const { user, loading } = useAuth()

  // If loading or no user, show button that opens auth dialog
  if (loading || !user) {
    return (
      <button onClick={() => onAuthClick('signin')} className={className}>
        {children}
      </button>
    )
  }

  // If user is signed in, show link to profile
  return (
    <Link href="/profile" className={className}>
      {children}
    </Link>
  )
}

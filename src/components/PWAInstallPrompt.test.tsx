import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, fireEvent, act } from '@testing-library/react'
import '@testing-library/jest-dom/vitest'
import * as React from 'react'
import { PWAInstallPrompt } from './PWAInstallPrompt'

// --- Mocks ---
vi.mock('@/components/Button', () => ({
  Button: ({
    children,
    onClick,
  }: { children: React.ReactNode; onClick?: () => void }) => (
    <button data-testid="install-button-mock" onClick={onClick}>
      {children}
    </button>
  ),
}))
// --- End Mocks ---

// Helper to dispatch the beforeinstallprompt event
const mockDeferredPrompt = {
  prompt: vi.fn(() => Promise.resolve()),
  userChoice: Promise.resolve({ outcome: 'accepted', platform: 'web' }),
}
const dispatchBeforeInstallPrompt = () => {
  const event = new Event('beforeinstallprompt')
  Object.assign(event, mockDeferredPrompt)
  act(() => {
    window.dispatchEvent(event)
  })
}

describe('PWAInstallPrompt Component', () => {
  let matchMediaMock: any
  let localStorageMock: any

  beforeEach(() => {
    vi.clearAllMocks()

    // Mock window.matchMedia
    matchMediaMock = vi.fn().mockImplementation((query: string) => ({
      matches: query === '(display-mode: standalone)' ? false : false, // Default: not installed
      media: query,
      onchange: null,
      addListener: vi.fn(), // Deprecated
      removeListener: vi.fn(), // Deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    }))
    Object.defineProperty(window, 'matchMedia', {
      value: matchMediaMock,
      writable: true,
    })

    // Mock localStorage
    const store: Record<string, string> = {}
    localStorageMock = {
      getItem: vi.fn((key) => store[key] || null),
      setItem: vi.fn((key, value) => {
        store[key] = value.toString()
      }),
      clear: vi.fn(() => {
        Object.keys(store).forEach((key) => delete store[key])
      }),
      removeItem: vi.fn((key) => delete store[key]),
      length: Object.keys(store).length,
      key: vi.fn((index) => Object.keys(store)[index] || null),
    }
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true,
    })

    // Reset deferredPrompt mock
    mockDeferredPrompt.prompt.mockClear()
  })

  afterEach(() => {
    // Clean up mocks if necessary, though beforeEach should handle most
  })

  it('should render null if already installed', () => {
    matchMediaMock.mockImplementation((query: string) => ({
      matches: query === '(display-mode: standalone)' ? true : false, // Simulate installed
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    }))
    render(<PWAInstallPrompt />)
    expect(screen.queryByText(/install app/i)).toBeNull()
  })

  it('should render null if previously dismissed', () => {
    localStorageMock.getItem.mockReturnValue('true') // Simulate dismissed
    render(<PWAInstallPrompt />)
    expect(screen.queryByText(/install app/i)).toBeNull()
  })

  it('should render null initially before beforeinstallprompt event', () => {
    render(<PWAInstallPrompt />)
    expect(screen.queryByText(/install app/i)).toBeNull()
  })

  it('should render the install prompt when beforeinstallprompt fires and not installed/dismissed', () => {
    render(<PWAInstallPrompt />)
    dispatchBeforeInstallPrompt() // Fire the event

    expect(screen.getByText(/install app/i)).toBeTruthy()
    expect(screen.getByText(/install armwrestling power arena/i)).toBeTruthy()
    expect(screen.getByRole('button', { name: /install/i })).toBeTruthy()
    expect(screen.getByRole('button', { name: /minimize/i })).toBeTruthy()
    expect(screen.getByRole('button', { name: /close/i })).toBeTruthy()
  })

  it('should call deferredPrompt.prompt when install button is clicked', async () => {
    render(<PWAInstallPrompt />)
    dispatchBeforeInstallPrompt()

    const installButton = screen.getByRole('button', { name: /install/i })
    await act(async () => {
      fireEvent.click(installButton)
    })

    expect(mockDeferredPrompt.prompt).toHaveBeenCalledTimes(1)
    // The component should hide after install attempt
    expect(screen.queryByText(/install app/i)).toBeNull()
  })

  it('should minimize the prompt when minimize button is clicked', () => {
    render(<PWAInstallPrompt />)
    dispatchBeforeInstallPrompt()

    const minimizeButton = screen.getByRole('button', { name: /minimize/i })
    act(() => {
      fireEvent.click(minimizeButton)
    })

    // Prompt text should disappear
    expect(screen.queryByText(/install app/i)).toBeNull()
    // Minimized icon button should appear (check by title or structure)
    expect(screen.getByTitle(/install app/i)).toBeTruthy()
  })

  it('should restore the prompt when minimized icon is clicked', () => {
    render(<PWAInstallPrompt />)
    dispatchBeforeInstallPrompt()

    // Minimize first
    const minimizeButton = screen.getByRole('button', { name: /minimize/i })
    act(() => {
      fireEvent.click(minimizeButton)
    })
    expect(screen.queryByText(/install app/i)).toBeNull()

    // Click the minimized icon
    const minimizedIcon = screen.getByTitle(/install app/i)
    act(() => {
      fireEvent.click(minimizedIcon)
    })

    // Prompt should reappear
    expect(screen.getByText(/install app/i)).toBeTruthy()
  })

  it('should dismiss the prompt and set localStorage when close button is clicked', () => {
    render(<PWAInstallPrompt />)
    dispatchBeforeInstallPrompt()

    const closeButton = screen.getByRole('button', { name: /close/i })
    act(() => {
      fireEvent.click(closeButton)
    })

    // Prompt should disappear
    expect(screen.queryByText(/install app/i)).toBeNull()
    // Check localStorage mock
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'pwa-prompt-dismissed',
      'true',
    )
  })
})

import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom/vitest'
import * as React from 'react'
import { Button } from './button'

describe('Button Component (ShadCN)', () => {
  it('should render a button element', () => {
    render(<Button>Click Me</Button>)
    const button = screen.getByRole('button', { name: /click me/i })
    expect(button).toBeInTheDocument()
  })

  it('should apply default variant classes', () => {
    render(<Button>Default Button</Button>)
    const button = screen.getByRole('button')
    // Check for a class specific to the default variant (adjust if needed)
    expect(button).toHaveClass('bg-primary')
  })

  it('should apply specified variant classes', () => {
    render(<Button variant="destructive">Destructive Button</Button>)
    const button = screen.getByRole('button')
    // Check for a class specific to the destructive variant
    expect(button).toHaveClass('bg-destructive')
  })

  it('should apply specified size classes', () => {
    render(<Button size="lg">Large Button</Button>)
    const button = screen.getByRole('button')
    // Check for a class specific to the large size
    expect(button).toHaveClass('h-10') // Example size class, adjust as needed
  })

  it('should render as a link when asChild and anchor tag are used', () => {
    render(
      <Button asChild>
        <a href="/link">Link Button</a>
      </Button>,
    )
    const link = screen.getByRole('link', { name: /link button/i })
    expect(link).toBeInTheDocument()
    expect(link).toHaveAttribute('href', '/link')
    // Check if button classes are applied to the anchor
    expect(link).toHaveClass('bg-primary')
  })

  it('should be disabled when disabled prop is true', () => {
    render(<Button disabled>Disabled Button</Button>)
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
  })
})

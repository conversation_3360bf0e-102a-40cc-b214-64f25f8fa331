import { createClient } from '@/lib/supabase/server'
import Image from 'next/image'

type MedalType = {
  type: string
  name: string
  color: string
  description: string
}

// Function to get medal counts from the database
async function getMedalCounts() {
  try {
    const supabase = createClient()

    // Query to count medals by type
    const { data, error } = await supabase.from('medals').select('medal_type')

    if (error) {
      console.error('Error fetching medal counts:', error)
      return null
    }

    // Count medals by type
    const counts: Record<string, number> = {}
    for (const medal of data) {
      counts[medal.medal_type] = (counts[medal.medal_type] || 0) + 1
    }

    return counts
  } catch (error) {
    console.error('Failed to fetch medal counts:', error)
    return null
  }
}

export default async function MedalSystemSection() {
  // Define medal types with their display properties
  const medalTypes: MedalType[] = [
    {
      type: 'bronze',
      name: 'Bronze Medal',
      color: 'from-amber-700 to-yellow-600',
      description: '',
    },
    {
      type: 'silver',
      name: 'Silver Medal',
      color: 'from-gray-400 to-gray-300',
      description: '',
    },
    {
      type: 'gold',
      name: 'Gold Medal',
      color: 'from-yellow-500 to-yellow-300',
      description: '',
    },
    {
      type: 'platinum',
      name: 'Platinum Medal',
      color: 'from-gray-300 to-white',
      description: '',
    },
    {
      type: 'diamond',
      name: 'Diamond Medal',
      color: 'from-blue-400 to-purple-500',
      description: '',
    },
  ]

  // Get medal counts from the database
  const medalCounts = await getMedalCounts()

  return (
    <section className="bg-white py-16 dark:bg-gray-900">
      <div className="container mx-auto px-4 text-center">
        <h2 className="mb-8 text-3xl font-bold text-gray-900 md:text-4xl dark:text-white">
          Medal System
        </h2>
        <p className="mx-auto mb-10 max-w-3xl text-lg text-gray-800 dark:text-gray-200">
          Earn prestigious medals based on your performance in various exercises
          and competitions. From Bronze to Diamond, showcase your achievements
          and climb the ranks.
        </p>

        {/* Adjusted gap and alignment */}
        <div className="flex flex-wrap items-start justify-center gap-8 md:gap-12">
          {medalTypes.map((medal) => (
            // Added flex column layout, width constraint, and vertical spacing
            <div
              key={medal.type}
              className="flex w-40 flex-col items-center space-y-2 text-center"
            >
              {/* Removed relative positioning and margin bottom */}
              <div className="mx-auto">
                <Image
                  src={`/images/medals/${medal.type}.png`}
                  alt={medal.name}
                  width={160} // Increased size
                  height={160} // Increased size
                  // Adjusted size classes
                  className="h-32 w-32 object-contain md:h-40 md:w-40"
                />
              </div>
              {/* <p className="mt-2 text-lg font-semibold text-gray-900 dark:text-white">
                {medal.name}
              </p>
              <p className="mt-1 max-w-[160px] text-sm text-gray-600 dark:text-gray-400">
                {medal.description}
              </p>
              {medalCounts && (
                <div className="mt-1 text-center">
                  <span className="text-xs font-medium text-gray-500 dark:text-gray-500">
                    ({medalCounts[medal.type] || 0} awarded)
                  </span>
                </div>
              )} */}
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

import { Button } from '@/components/Button'
import Link from 'next/link'

export default function HeroSection() {
  return (
    <div className="relative min-h-[70vh] flex items-center justify-center bg-gradient-to-br from-purple-800 via-blue-700 to-emerald-600 dark:from-purple-900 dark:via-blue-800 dark:to-emerald-700">
      <div className="absolute inset-0 bg-white dark:bg-gray-900 opacity-30"></div>
      <div className="w-full py-16 relative z-10 text-center text-white">
        <h1 className="text-5xl md:text-7xl font-bold mb-6 tracking-wider">
          ARMWRESTLING POWER ARENA
        </h1>
        <h2 className="text-2xl md:text-3xl font-semibold mb-4">
          Showcase your armwrestling strength
        </h2>
        <p className="text-lg md:text-xl mb-8 max-w-3xl mx-auto">
          Join the ultimate platform for armwrestlers to track progress, compete
          globally, and earn recognition through our medal system. Submit your
          exercises, climb the rankings, and become a champion.
        </p>
        <Link href="/exercises">
          <Button size="lg" className="text-xl py-6 px-10 rounded-md">
            ENTER THE ARENA
          </Button>
        </Link>
      </div>
    </div>
  )
}

import {
  Card,
  CardDescription,
  <PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from '@/components/Card'
import { Button } from '@/components/Button'
import Link from 'next/link'
import { createClient } from '@/lib/supabase/server'

type Exercise = {
  id: string
  title: string
  description: string
  image_url?: string
}

async function getFeaturedExercises(): Promise<Exercise[]> {
  try {
    const supabase = createClient()

    // Get featured exercises
    const { data, error } = await supabase
      .from('exercises')
      .select('id, title, description, video_tutorial_url')
      .limit(3)

    if (error) {
      console.error('Error fetching exercises:', error)
      return []
    }

    // Transform data into the exercise format
    return data.map((exercise) => ({
      id: exercise.id,
      title: exercise.title,
      description: exercise.description,
      image_url: exercise.video_tutorial_url
        ? `https://img.youtube.com/vi/${getYouTubeVideoId(
            exercise.video_tutorial_url,
          )}/hqdefault.jpg`
        : '/exercises/default.jpg',
    }))
  } catch (error) {
    console.error('Failed to fetch exercises:', error)
    return []
  }
}

// Helper function to extract YouTube video ID from URL
function getYouTubeVideoId(url: string): string {
  // Handle different YouTube URL formats
  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/
  const match = url.match(regExp)
  return match && match[2].length === 11 ? match[2] : ''
}

export default async function ExercisesSection() {
  const exercises = await getFeaturedExercises()

  return (
    <section className="py-16 bg-gray-100 dark:bg-gray-800">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold mb-8 text-center text-gray-900 dark:text-white">
          Featured Exercises
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {exercises.length === 0 ? (
            <div className="col-span-3 text-center py-10">
              <p className="text-gray-500 dark:text-gray-400">
                No exercises available at the moment.
              </p>
            </div>
          ) : (
            exercises.map((exercise) => (
              <Card
                key={exercise.id}
                className="overflow-hidden h-full flex flex-col hover:shadow-xl transition-shadow duration-300"
              >
                <div className="relative h-64 w-full bg-gray-300 overflow-hidden">
                  {exercise.image_url ? (
                    <img
                      src={exercise.image_url}
                      alt={exercise.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-gray-700 to-gray-900 flex items-center justify-center">
                      <span className="text-white text-lg font-medium">
                        Exercise Image
                      </span>
                    </div>
                  )}
                </div>
                <CardHeader>
                  <CardTitle>{exercise.title}</CardTitle>
                  <CardDescription>{exercise.description}</CardDescription>
                </CardHeader>
                <CardFooter className="mt-auto">
                  <Link href={`/exercises/${exercise.id}`} className="w-full">
                    <Button className="w-full">View Details</Button>
                  </Link>
                </CardFooter>
              </Card>
            ))
          )}
        </div>
        <div className="mt-10 text-center">
          <Link href="/exercises">
            <Button variant="secondary">Explore All Exercises</Button>
          </Link>
        </div>
      </div>
    </section>
  )
}

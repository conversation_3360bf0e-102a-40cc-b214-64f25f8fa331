import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>it<PERSON>,
} from '@/components/Card'
import { Button } from '@/components/Button'
import Link from 'next/link'
import RankingsTable from '@/components/rankings/RankingsTable'
import { fetchMaleRankings, fetchFemaleRankings } from '@/lib/rankings'

export default async function RankingsSection() {
  // Fetch top 10 male and female athletes
  const [maleRankings, femaleRankings] = await Promise.all([
    fetchMaleRankings({ limit: 10 }),
    fetchFemaleRankings({ limit: 10 }),
  ])

  return (
    <section className="py-16 bg-white dark:bg-gray-900">
      <div className="w-full">
        <h2 className="text-3xl md:text-4xl font-bold mb-8 text-center">
          Global Rankings
        </h2>

        <div className="grid md:grid-cols-2 gap-8 max-w-7xl mx-auto">
          {/* Male Rankings */}
          <Card className="overflow-hidden shadow-lg">
            <CardHeader className="bg-gray-50 dark:bg-gray-800">
              <CardTitle className="text-center">Top Male Athletes</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <RankingsTable users={maleRankings} isCondensed={true} />
            </CardContent>
            <CardFooter className="flex justify-center p-4">
              <Link href="/rankings?gender=male">
                <Button variant="secondary">View All Male Rankings</Button>
              </Link>
            </CardFooter>
          </Card>

          {/* Female Rankings */}
          <Card className="overflow-hidden shadow-lg">
            <CardHeader className="bg-gray-50 dark:bg-gray-800">
              <CardTitle className="text-center">Top Female Athletes</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <RankingsTable users={femaleRankings} isCondensed={true} />
            </CardContent>
            <CardFooter className="flex justify-center p-4">
              <Link href="/rankings?gender=female">
                <Button variant="secondary">View All Female Rankings</Button>
              </Link>
            </CardFooter>
          </Card>
        </div>

        <div className="flex justify-center mt-8">
          <Link href="/rankings">
            <Button variant="default" size="lg">
              View Full Rankings
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
}

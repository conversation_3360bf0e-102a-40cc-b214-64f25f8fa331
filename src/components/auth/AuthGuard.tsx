/**
 * AuthGuard.tsx
 *
 * This component conditionally renders content based on authentication state or permissions.
 * It's useful for showing/hiding UI elements based on user permissions.
 */

'use client'

import { useAuth } from '@/contexts/AuthContext'
import { usePermissions } from '@/hooks/usePermissions'
import { Permission } from '@/lib/permissions/types'
import { ReactNode } from 'react'

interface AuthGuardProps {
  children: ReactNode
  fallback?: ReactNode
  requiredPermission?: Permission
  requiredPermissions?: Permission[]
  requireAllPermissions?: boolean
  requireAuth?: boolean
  showWhenUnauthorized?: boolean
}

export function AuthGuard({
  children,
  fallback,
  requiredPermission,
  requiredPermissions = [],
  requireAllPermissions = false,
  requireAuth = true,
  showWhenUnauthorized = false,
}: AuthGuardProps) {
  const { isAuthenticated, loading } = useAuth()
  const permissions = usePermissions()

  // Add single permission to array if provided
  if (requiredPermission) {
    requiredPermissions = [...requiredPermissions, requiredPermission]
  }

  // If still loading, don't render anything
  if (loading) {
    return null
  }

  // Check authentication requirement
  if (requireAuth && !isAuthenticated) {
    return showWhenUnauthorized ? (
      <>{children}</>
    ) : fallback ? (
      <>{fallback}</>
    ) : null
  }

  // If no permissions required, just check authentication
  if (requiredPermissions.length === 0) {
    return isAuthenticated ? <>{children}</> : fallback ? <>{fallback}</> : null
  }

  // Check permissions
  const hasRequiredPermissions = requireAllPermissions
    ? permissions.canAll(requiredPermissions)
    : permissions.canAny(requiredPermissions)

  // Render based on permissions
  if (hasRequiredPermissions) {
    return <>{children}</>
  } else if (showWhenUnauthorized) {
    return <>{children}</>
  } else {
    return fallback ? <>{fallback}</> : null
  }
}

'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/Button'
import { Download, X, Minimize2 } from 'lucide-react'

// Interface for the BeforeInstallPromptEvent
interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed'; platform: string }>
}

export function PWAInstallPrompt() {
  const [isInstallable, setIsInstallable] = useState(false)
  const [deferredPrompt, setDeferredPrompt] =
    useState<BeforeInstallPromptEvent | null>(null)
  const [isInstalled, setIsInstalled] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const [isDismissed, setIsDismissed] = useState(false)

  useEffect(() => {
    // Check if the app is already installed
    if (window.matchMedia('(display-mode: standalone)').matches) {
      setIsInstalled(true)
      return
    }

    // Check if user has previously dismissed the prompt
    const promptDismissed = localStorage.getItem('pwa-prompt-dismissed')
    if (promptDismissed === 'true') {
      setIsDismissed(true)
    }

    // Listen for the beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      // Prevent Chrome 67 and earlier from automatically showing the prompt
      e.preventDefault()
      // Store the event so it can be triggered later
      setDeferredPrompt(e as BeforeInstallPromptEvent)
      // Show the install button
      setIsInstallable(true)
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)

    // Cleanup
    return () => {
      window.removeEventListener(
        'beforeinstallprompt',
        handleBeforeInstallPrompt,
      )
    }
  }, [])

  // Handle app installation
  const handleInstallClick = async () => {
    if (!deferredPrompt) return

    // Show the install prompt
    await deferredPrompt.prompt()

    // Wait for the user to respond to the prompt
    const choiceResult = await deferredPrompt.userChoice

    if (choiceResult.outcome === 'accepted') {
      console.log('User accepted the install prompt')
      setIsInstalled(true)
    } else {
      console.log('User dismissed the install prompt')
    }

    // Clear the deferredPrompt
    setDeferredPrompt(null)
    setIsInstallable(false)
  }

  // Toggle minimized state
  const toggleMinimize = () => {
    setIsMinimized(!isMinimized)
  }

  // Dismiss the prompt
  const dismissPrompt = () => {
    setIsDismissed(true)
    localStorage.setItem('pwa-prompt-dismissed', 'true')
  }

  if (!isInstallable || isInstalled || isDismissed) return null

  if (isMinimized) {
    return (
      <div
        className="fixed bottom-4 left-4 z-50 p-2 bg-white dark:bg-gray-800 rounded-full shadow-lg border border-gray-200 dark:border-gray-700 cursor-pointer"
        onClick={toggleMinimize}
        title="Install App"
      >
        <Download className="h-6 w-6 text-primary" />
      </div>
    )
  }

  return (
    <div className="fixed bottom-4 left-4 right-4 z-50 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 md:max-w-md md:left-4 md:right-auto">
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-4">
          <div className="flex-1">
            <h3 className="font-semibold text-lg mb-1">Install App</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Install Armwrestling Power Arena for a better experience
            </p>
          </div>
          <Button
            onClick={handleInstallClick}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Install
          </Button>
        </div>
        <div className="flex items-center ml-2">
          <button
            className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 transition-colors"
            onClick={toggleMinimize}
            title="Minimize"
          >
            <Minimize2 className="h-4 w-4" />
          </button>
          <button
            className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 transition-colors"
            onClick={dismissPrompt}
            title="Close"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  )
}

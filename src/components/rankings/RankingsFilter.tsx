'use client'

import React, { useState } from 'react'
import { Button } from '@/components/Button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu'
import { cn } from '@/lib/utils'

type FilterOption = {
  label: string
  value: string
}

type RankingsFilterProps = {
  title: string
  options: FilterOption[]
  defaultValue?: string
  onChange: (value: string) => void
  className?: string
}

export default function RankingsFilter({
  title,
  options,
  defaultValue,
  onChange,
  className,
}: RankingsFilterProps) {
  const initialValue = defaultValue || options[0]?.value || ''
  const [selectedValue, setSelectedValue] = useState<string>(initialValue)

  const handleChange = (value: string) => {
    if (value) {
      setSelectedValue(value)
      onChange(value)
    }
  }

  const selectedLabel =
    options.find((option) => option.value === selectedValue)?.label || title

  return (
    <div className={cn(className)}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="flex items-center gap-1">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 16 16"
              fill="currentColor"
              className="w-4 h-4 opacity-50"
            >
              <path
                fillRule="evenodd"
                d="M8 2a.75.75 0 0 1 .75.75v4.5h4.5a.75.75 0 0 1 0 1.5h-4.5v4.5a.75.75 0 0 1-1.5 0v-4.5h-4.5a.75.75 0 0 1 0-1.5h4.5v-4.5A.75.75 0 0 1 8 2Z"
                clipRule="evenodd"
              />
            </svg>
            {selectedLabel}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56">
          <DropdownMenuRadioGroup
            value={selectedValue}
            onValueChange={handleChange}
          >
            {options.map((option) => (
              <DropdownMenuRadioItem key={option.value} value={option.value}>
                {option.label}
              </DropdownMenuRadioItem>
            ))}
          </DropdownMenuRadioGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

export function GenderFilter({
  onChange,
  defaultValue = 'all',
  className,
}: {
  onChange: (value: string) => void
  defaultValue?: string
  className?: string
}) {
  return (
    <RankingsFilter
      title="Gender"
      options={[
        { label: 'All sexes', value: 'all' },
        { label: 'Men', value: 'male' },
        { label: 'Women', value: 'female' },
      ]}
      defaultValue={defaultValue}
      onChange={onChange}
      className={className}
    />
  )
}

export function WeightCategoryFilter({
  onChange,
  defaultValue = 'all',
  className,
}: {
  onChange: (value: string) => void
  defaultValue?: string
  className?: string
}) {
  return (
    <RankingsFilter
      title="Weight Category"
      options={[
        { label: 'All weights', value: 'all' },
        { label: 'Under 95kg', value: 'under_95kg' },
        { label: 'Over 95kg', value: 'over_95kg' },
      ]}
      defaultValue={defaultValue}
      onChange={onChange}
      className={className}
    />
  )
}

export function CountryFilter({
  countries,
  onChange,
  defaultValue = 'all',
  className,
}: {
  countries: string[]
  onChange: (value: string) => void
  defaultValue?: string
  className?: string
}) {
  const countryOptions = [
    { label: 'All Countries', value: 'all' },
    ...countries.map((country) => ({
      label: country,
      value: country,
    })),
  ]

  return (
    <RankingsFilter
      title="Country"
      options={countryOptions}
      defaultValue={defaultValue}
      onChange={onChange}
      className={className}
    />
  )
}

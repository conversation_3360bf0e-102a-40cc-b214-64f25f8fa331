'use client'

import { useState } from 'react'
import Link from 'next/link'
import { VideoOff } from 'lucide-react'

import { Button } from '@/components/Button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/Card'
import PageLayout from '@/components/PageLayout'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

// Define the Exercise type here or import it if defined elsewhere
// For now, defining it here to make the component self-contained
type Exercise = {
  id: string
  title: string
  description: string
  video_tutorial_url: string
  equipment_required: string[]
  created_by: string
  created_at: string
  updated_at: string
}

// Helper function (can also be imported if shared)
function getYouTubeVideoId(url: string): string {
  if (!url) return ''
  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/
  const match = url.match(regExp)
  return match && match[2].length === 11 ? match[2] : ''
}

export function ExerciseClientPage({ exercise }: { exercise: Exercise }) {
  const [videoUrl, setVideoUrl] = useState('')

  const videoId = getYouTubeVideoId(exercise.video_tutorial_url)

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    // TODO: Implement actual video submission logic
    console.log('Submitting video URL:', videoUrl)
    // Optionally clear the input after submission
    // setVideoUrl("");
  }

  return (
    <PageLayout>
      {/* Layout remains the same as previous version */}
      <div className="mx-auto max-w-6xl space-y-10 px-4 py-8 sm:px-6 lg:px-8">
        <div className="mb-10 flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl">
            {exercise.title}
          </h1>
          <Button variant="outline" asChild>
            <Link href="/exercises">Back to Exercises</Link>
          </Button>
        </div>

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3 lg:gap-10">
          <div className="space-y-8 lg:col-span-2">
            {videoId ? (
              <div className="aspect-video w-full overflow-hidden rounded-xl border bg-black shadow-lg dark:border-gray-700">
                <iframe
                  width="100%"
                  height="100%"
                  src={`https://www.youtube.com/embed/${videoId}`}
                  title="YouTube video player"
                  style={{ border: 'none' }}
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                  allowFullScreen
                ></iframe>
              </div>
            ) : (
              <div className="flex aspect-video w-full flex-col items-center justify-center rounded-xl border border-dashed bg-gray-100 shadow-inner dark:border-gray-700 dark:bg-gray-800">
                <VideoOff
                  className="mb-3 h-12 w-12 text-gray-400 dark:text-gray-500"
                  aria-hidden="true"
                />
                <span className="px-4 text-center font-medium text-gray-500 dark:text-gray-400">
                  Video Tutorial Unavailable
                </span>
              </div>
            )}

            <Card className="overflow-hidden transition-shadow duration-300 hover:shadow-xl">
              <CardHeader>
                <CardTitle className="text-xl">Submit Your Video</CardTitle>
                <CardDescription>
                  Paste the link to your performance video below.
                </CardDescription>
              </CardHeader>
              <form onSubmit={handleSubmit}>
                <CardContent className="space-y-4 px-6 pt-2 pb-5">
                  <div className="space-y-1.5">
                    <Label htmlFor="videoUrl">Video Link</Label>
                    <Input
                      id="videoUrl"
                      type="url"
                      placeholder="e.g., https://youtube.com/watch?v=..."
                      value={videoUrl}
                      onChange={(e) => setVideoUrl(e.target.value)}
                      required
                      className="transition-colors duration-200 focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                  <Button type="submit" className="w-full">
                    Submit Video
                  </Button>
                </CardContent>
              </form>
            </Card>
          </div>

          <div className="space-y-8 lg:col-span-1">
            <Card className="overflow-hidden transition-shadow duration-300 hover:shadow-xl">
              <CardHeader>
                <CardTitle className="text-xl">Description</CardTitle>
              </CardHeader>
              <CardContent className="px-6 py-5">
                <p className="leading-relaxed text-gray-700 dark:text-gray-300">
                  {exercise.description}
                </p>
              </CardContent>
            </Card>

            <Card className="overflow-hidden transition-shadow duration-300 hover:shadow-xl">
              <CardHeader>
                <CardTitle className="text-xl">Equipment Required</CardTitle>
              </CardHeader>
              <CardContent className="px-6 py-5">
                {exercise.equipment_required &&
                Array.isArray(exercise.equipment_required) &&
                exercise.equipment_required.length > 0 ? (
                  <ul className="space-y-2.5">
                    {exercise.equipment_required.map(
                      (item: string, index: number) => (
                        <li
                          key={index}
                          className="flex items-center text-gray-700 dark:text-gray-300"
                        >
                          <span className="mr-2 inline-block h-1.5 w-1.5 rounded-full bg-gray-500 dark:bg-gray-400"></span>
                          {item}
                        </li>
                      ),
                    )}
                  </ul>
                ) : (
                  <p className="text-gray-600 dark:text-gray-400">
                    No specific equipment required.
                  </p>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </PageLayout>
  )
}

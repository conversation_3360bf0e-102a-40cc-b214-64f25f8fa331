import { isPathAllowed } from '@/lib/permissions/acl'
import { createClient } from '@/lib/supabase/middleware'
import { NextResponse, type NextRequest } from 'next/server'

/**
 * Middleware for RBAC route protection.
 * - Fetches user session and role from Supabase.
 * - Uses central ACL to restrict access to protected routes.
 * - Redirects unauthorized users to home.
 * - Optionally sets x-user-role header for downstream use.
 */
export async function middleware(request: NextRequest) {
  const supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createClient(request, supabaseResponse)

  // Do not run code between createClient and supabase.auth.getUser()
  const {
    data: { user },
  } = await supabase.auth.getUser()

  const pathname = request.nextUrl.pathname

  // If not logged in, allow public routes, else redirect to login
  const PUBLIC_PREFIXES = [
    '/login',
    '/auth',
    // "/public",
    '/exercises',
    '/rankings',
    '/submissions', // Allow viewing submissions list
    // "/profile", // public profile pages
    '/',
  ]
  const startsWithAny = (arr: string[]) =>
    arr.some((prefix) =>
      prefix === '/' ? pathname === '/' : pathname.startsWith(prefix),
    )
  const isPublic = startsWithAny(PUBLIC_PREFIXES)

  if (!user && !isPublic) {
    // Not authenticated, redirect to login
    const loginUrl = new URL('/login', request.nextUrl.origin)
    return NextResponse.redirect(loginUrl)
  }

  // If logged in, fetch role and check ACL
  if (user) {
    // Fetch role from profiles
    let userRole: string | null = null
    try {
      const { data } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single()
      if (data && typeof data.role === 'string') {
        userRole = data.role
      }
    } catch {
      // ignore, treat as no role
    }

    // If role is present and not allowed for this path, redirect to home
    if (
      userRole &&
      !isPathAllowed(userRole as 'admin' | 'grandmaster' | 'athlete', pathname)
    ) {
      const homeUrl = new URL('/', request.nextUrl.origin)
      return NextResponse.redirect(homeUrl)
    }

    // Optionally: set x-user-role header for downstream use
    if (userRole) {
      supabaseResponse.headers.set('x-user-role', userRole)
    }
  }

  // Always return the supabaseResponse object
  return supabaseResponse
}

export const config = {
  matcher: [
    // Protect all routes except static assets, images, favicon, and public paths
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}

'use client'

import { useEffect } from 'react'
import { Button } from '@/components/Button'
import Link from 'next/link'

export default function ProfileError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Profile page error:', error)
  }, [error])

  // Check for common errors and provide specific messages
  let errorMessage = 'An error occurred while loading the profile.'
  let errorDescription =
    'Please try again or contact support if the problem persists.'

  if (error.message.includes('invalid input syntax for type uuid')) {
    errorMessage = 'Invalid Profile ID Format'
    errorDescription = 'The profile ID must be in a valid UUID format.'
  } else if (error.message.includes('not found')) {
    errorMessage = 'Profile Not Found'
    errorDescription = 'The profile you are looking for does not exist.'
  } else if (error.message.includes('permission denied')) {
    errorMessage = 'Access Denied'
    errorDescription = 'You do not have permission to view this profile.'
  }

  return (
    <div className="container mx-auto py-16 px-4 flex flex-col items-center justify-center">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 max-w-md w-full">
        <h2 className="text-2xl font-bold text-red-600 dark:text-red-400 mb-4">
          {errorMessage}
        </h2>
        <p className="mb-6 text-gray-600 dark:text-gray-300">
          {errorDescription}
        </p>
        <div className="flex flex-col sm:flex-row gap-4">
          <Button onClick={reset} variant="default">
            Try Again
          </Button>
          <Button asChild variant="outline">
            <Link href="/">Return Home</Link>
          </Button>
        </div>
      </div>
    </div>
  )
}

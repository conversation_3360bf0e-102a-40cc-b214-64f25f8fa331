'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { createBrowserClient } from '@supabase/ssr'
import {
  Card,
  CardContent,
  CardTitle,
  CardDescription,
} from '@/components/ui/card'

export default function CreateProfilePage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [isCreating, setIsCreating] = useState(false)

  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  )

  useEffect(() => {
    const checkProfileAndCreate = async () => {
      if (loading || !user) return

      // Check if profile already exists
      const { data: existingProfile } = await supabase
        .from('profiles')
        .select('id')
        .eq('auth_user_id', user.id)
        .single()

      // If profile exists, redirect to it
      if (existingProfile?.id) {
        router.push(`/profile/${existingProfile.id}`)
        return
      }

      // Otherwise start profile creation process
      setIsCreating(true)

      // Demo code - in a real app you'd collect more info from the user
      // Simulating a delay
      setTimeout(() => {
        // TODO: Implement actual profile creation
        router.push('/')
      }, 2000)
    }

    checkProfileAndCreate()
  }, [user, loading, router, supabase])

  // Show loading state while checking auth
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    )
  }

  // If not authenticated, show a message prompting to sign in
  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <Card className="max-w-md mx-auto">
          <CardContent className="pt-6">
            <CardTitle className="text-2xl font-bold">
              Sign In Required
            </CardTitle>
            <CardDescription className="mt-2">
              Please sign in to create your profile.
            </CardDescription>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container max-w-md mx-auto py-10">
      <Card>
        <CardContent className="pt-6">
          <CardTitle className="text-2xl font-bold">
            Create Your Profile
          </CardTitle>
          <CardDescription className="mt-2">
            {isCreating
              ? 'Setting up your profile...'
              : 'Please wait while we set up your account.'}
          </CardDescription>

          {isCreating && (
            <div className="flex justify-center mt-6">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          )}

          <p className="mt-4 text-sm text-gray-500">
            Your profile is being created. In a full implementation, this page
            would include a form to collect additional profile information.
          </p>

          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 text-sm rounded-md">
            Note: This is a placeholder page. Profile creation functionality
            will be implemented in a future update.
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

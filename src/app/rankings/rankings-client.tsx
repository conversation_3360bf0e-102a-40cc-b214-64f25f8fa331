'use client'

import { useRouter, usePathname } from 'next/navigation'
import { useCallback } from 'react'
import {
  GenderFilter,
  WeightCategoryFilter,
  CountryFilter,
} from '@/components/rankings/RankingsFilter'

type RankingsPageClientProps = {
  countries: string[]
  initialGender: string
  initialWeightCategory: string
  initialCountry: string
}

export default function RankingsPageClient({
  countries,
  initialGender,
  initialWeightCategory,
  initialCountry,
}: RankingsPageClientProps) {
  const router = useRouter()
  const pathname = usePathname()

  // Update URL with filters
  const updateFilters = useCallback(
    (params: Record<string, string>) => {
      // Get current URL search params
      const searchParams = new URLSearchParams(window.location.search)

      // Update search params with new values
      Object.entries(params).forEach(([key, value]) => {
        if (value === 'all') {
          searchParams.delete(key)
        } else {
          searchParams.set(key, value)
        }
      })

      // Create the new URL
      const newUrl = `${pathname}?${searchParams.toString()}`

      // Update the URL without refreshing the page
      router.push(newUrl)
    },
    [pathname, router],
  )

  // Handle gender filter change
  const handleGenderChange = useCallback(
    (gender: string) => {
      updateFilters({ gender })
    },
    [updateFilters],
  )

  // Handle weight category filter change
  const handleWeightCategoryChange = useCallback(
    (weightCategory: string) => {
      updateFilters({ weightCategory })
    },
    [updateFilters],
  )

  // Handle country filter change
  const handleCountryChange = useCallback(
    (country: string) => {
      updateFilters({ country })
    },
    [updateFilters],
  )

  return (
    <div className="flex flex-wrap gap-2 items-center">
      <GenderFilter
        onChange={handleGenderChange}
        defaultValue={initialGender}
      />
      <WeightCategoryFilter
        onChange={handleWeightCategoryChange}
        defaultValue={initialWeightCategory}
      />
      <CountryFilter
        countries={countries}
        onChange={handleCountryChange}
        defaultValue={initialCountry}
      />
    </div>
  )
}

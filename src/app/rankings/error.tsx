'use client'

import { useEffect } from 'react'
import { But<PERSON> } from '@/components/Button'
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/Card'
import { AlertTriangle } from 'lucide-react'

export default function RankingsError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    console.error('Rankings section error:', error)
  }, [error])

  return (
    <>
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Global Rankings</h1>
        <p className="text-gray-500 dark:text-gray-400">
          See how you compare to other armwrestlers around the world
        </p>
      </div>

      <Card className="border-red-300 dark:border-red-700">
        <CardHeader className="bg-red-50 dark:bg-red-900/20">
          <CardTitle className="text-red-600 dark:text-red-400 flex items-center">
            <AlertTriangle className="mr-2 h-5 w-5" />
            Error Loading Rankings
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-6">
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            An error occurred while loading the rankings data. Please try again
            later.
          </p>
          {error.message && (
            <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-md overflow-auto max-h-[200px] text-sm font-mono mt-4">
              {error.message}
            </div>
          )}
        </CardContent>
        <CardFooter className="border-t border-gray-200 dark:border-gray-700 pt-4">
          <Button onClick={reset} className="w-full sm:w-auto">
            Try again
          </Button>
        </CardFooter>
      </Card>
    </>
  )
}

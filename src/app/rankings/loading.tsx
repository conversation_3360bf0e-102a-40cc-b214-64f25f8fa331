import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/Card'
import PageLayout from '@/components/PageLayout'

export default function RankingsLoading() {
  return (
    <PageLayout>
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Global Rankings</h1>
        <p className="text-gray-500 dark:text-gray-400">
          See how you compare to other armwrestlers around the world
        </p>
      </div>

      {/* Filters Card */}
      <Card className="mb-8 w-full overflow-hidden">
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded-md animate-pulse"></div>
            <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded-md animate-pulse"></div>
            <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded-md animate-pulse"></div>
          </div>
        </CardContent>
      </Card>

      {/* Rankings Card */}
      <Card className="w-full overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Athlete Rankings</CardTitle>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Loading...
          </div>
        </CardHeader>
        <CardContent className="px-6 py-4">
          <div className="flex items-center justify-center min-h-[400px]">
            <LoadingSpinner size="lg" />
          </div>
        </CardContent>
      </Card>
    </PageLayout>
  )
}

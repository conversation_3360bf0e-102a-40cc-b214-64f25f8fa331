import { SubmissionErrorBoundary } from '@/features/submissions/components/SubmissionErrorBoundary'
import { AdminSubmissionsManagementView } from '@/features/submissions/components/overview/admin-submissions-management-view'
import { AthleteSubmissionsView } from '@/features/submissions/components/overview/athlete-submissions-view'
import { UnauthenticatedSubmissionsView } from '@/features/submissions/components/overview/unauthenticated-submissions-view'
import { getSupabaseRouteHandlerClient } from '@/lib/supabase'
import type { SubmissionWithRelations } from '@/types/submission' // Ensure this path is correct
import type { Metadata } from 'next'
import { cookies } from 'next/headers'

export const metadata: Metadata = {
  title: 'Submissions | Arm Power Arena',
  description: 'View and submit exercise performances',
}

/**
 * Submissions Page Content
 * Displays different content based on user authentication status and role
 */
async function SubmissionsPageContent() {
  const cookieStore = await cookies()
  const supabase = getSupabaseRouteHandlerClient(cookieStore)

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    return <UnauthenticatedSubmissionsView />
  }

  const { data: profile } = await supabase
    .from('profiles')
    .select('role')
    .eq('id', user.id)
    .single()

  const userRole = profile?.role

  if (userRole === 'admin' || userRole === 'grandmaster') {
    const { data: submissions, error } = await supabase
      .from('submissions')
      .select(
        `
        *,
        exercises(id, title),
        profiles:user_id(username, full_name, avatar_url)
      `,
      )
      .order('submitted_at', { ascending: false })
      .limit(50) // Add pagination limit for performance
      .returns<SubmissionWithRelations[]>() // Add .returns for typed result

    if (error) {
      // Handle error fetching submissions, e.g., log it or show an error message
      console.error('Error fetching submissions for admin:', error.message)
      // Potentially return an error component or a fallback UI
      return <p>Error loading submissions. Please try again later.</p>
    }
    return <AdminSubmissionsManagementView submissions={submissions} />
  }

  return <AthleteSubmissionsView />
}

/**
 * Submissions Page
 * Displays different content based on user authentication status and role:
 * - Non-authenticated users: Welcome message and sign-in prompt
 * - Admin/Grandmaster: Submissions management interface
 * - Athletes: Link to their personal submissions
 *
 * Wrapped with error boundary for proper error handling
 */
export default async function SubmissionsPage() {
  return (
    <SubmissionErrorBoundary>
      <SubmissionsPageContent />
    </SubmissionErrorBoundary>
  )
}

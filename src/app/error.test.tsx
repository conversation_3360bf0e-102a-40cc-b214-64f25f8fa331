import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, fireEvent, act } from '@testing-library/react'
import '@testing-library/jest-dom/vitest'
import * as React from 'react'
import ErrorComponent from './error'

// --- Mocks ---
// Mock Card components
vi.mock('@/components/Card', () => ({
  Card: ({ children, ...props }: { children: React.ReactNode }) => (
    <div data-testid="card-mock" {...props}>
      {children}
    </div>
  ),
  CardHeader: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="card-header-mock">{children}</div>
  ),
  CardTitle: ({ children }: { children: React.ReactNode }) => (
    <h2 data-testid="card-title-mock">{children}</h2>
  ),
  CardContent: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="card-content-mock">{children}</div>
  ),
  CardFooter: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="card-footer-mock">{children}</div>
  ),
}))

// Mock Button component
vi.mock('@/components/Button', () => ({
  Button: ({
    children,
    onClick,
  }: { children: React.ReactNode; onClick?: () => void }) => (
    <button data-testid="button-mock" onClick={onClick}>
      {children}
    </button>
  ),
}))
// --- End Mocks ---

describe('Root Error Component (app/error.tsx)', () => {
  const mockReset = vi.fn()
  const mockError = new Error('Something went wrong here!')
  const mockErrorWithDigest = new Error('Another issue')
  ;(mockErrorWithDigest as any).digest = '12345' // Add digest property

  beforeEach(() => {
    vi.clearAllMocks()
    // Suppress console.error logs during tests
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    // Restore console.error
    vi.mocked(console.error).mockRestore()
  })

  it('should render the main error message and card structure', () => {
    render(<ErrorComponent error={mockError} reset={mockReset} />)

    expect(screen.getByTestId('card-mock')).toBeTruthy()
    expect(
      screen.getByRole('heading', { name: /something went wrong/i }),
    ).toBeTruthy()
    expect(screen.getByText(/an unexpected error occurred/i)).toBeTruthy()
  })

  it('should display the specific error message if provided', () => {
    render(<ErrorComponent error={mockError} reset={mockReset} />)
    expect(screen.getByText(mockError.message)).toBeTruthy()
  })

  it('should not display the error message section if error.message is empty', () => {
    const errorWithoutMessage = new Error('')
    render(<ErrorComponent error={errorWithoutMessage} reset={mockReset} />)
    const content = screen.getByTestId('card-content-mock')
    expect(content.querySelector('.font-mono')).toBeNull()
  })

  it('should render the "Try again" button', () => {
    render(<ErrorComponent error={mockError} reset={mockReset} />)
    const button = screen.getByRole('button', { name: /try again/i })
    expect(button).toBeTruthy()
  })

  it('should call the reset function when "Try again" button is clicked', async () => {
    render(<ErrorComponent error={mockError} reset={mockReset} />)
    const button = screen.getByRole('button', { name: /try again/i })

    await act(async () => {
      fireEvent.click(button)
    })

    expect(mockReset).toHaveBeenCalledTimes(1)
  })

  it('should log the error to the console on mount', () => {
    render(<ErrorComponent error={mockErrorWithDigest} reset={mockReset} />)
    expect(console.error).toHaveBeenCalledTimes(1)
    expect(console.error).toHaveBeenCalledWith(
      'Application error:',
      mockErrorWithDigest,
    )
  })
})

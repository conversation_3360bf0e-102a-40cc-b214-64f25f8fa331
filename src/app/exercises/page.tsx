import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from '@/components/Card'
import { But<PERSON> } from '@/components/Button'
import Link from 'next/link'
import { createServerClient } from '@/lib/supabase'
import PageLayout from '@/components/PageLayout'

type Exercise = {
  id: string
  title: string
  description: string
  video_tutorial_url: string
  equipment_required: string[]
  created_by: string
  created_at: string
  updated_at: string
}

export default async function ExercisesPage() {
  // Fetch exercises from Supabase
  const supabase = createServerClient()
  const { data: exercises, error } = await supabase
    .from('exercises')
    .select('*')

  if (error) {
    console.error('Error fetching exercises:', error)
  }

  // Fallback to empty array if no exercises found
  const exerciseList = exercises || []
  return (
    <PageLayout>
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Exercise Library</h1>
        <Button variant="secondary">Filter Exercises</Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {exerciseList.map((exercise) => (
          <Card
            key={exercise.id}
            className="overflow-hidden h-full flex flex-col hover:shadow-xl transition-shadow duration-300"
          >
            <div className="relative h-64 w-full bg-gray-300 overflow-hidden">
              {exercise.video_tutorial_url ? (
                <img
                  src={`https://img.youtube.com/vi/${getYouTubeVideoId(exercise.video_tutorial_url)}/hqdefault.jpg`}
                  alt={exercise.title}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-gray-700 to-gray-900 flex items-center justify-center">
                  <span className="text-white text-lg font-medium">
                    Exercise Image
                  </span>
                </div>
              )}
            </div>
            <CardHeader>
              <CardTitle>{exercise.title}</CardTitle>
              <CardDescription>{exercise.description}</CardDescription>
            </CardHeader>
            <CardFooter className="mt-auto">
              <Link href={`/exercises/${exercise.id}`} className="w-full">
                <Button className="w-full">View Details</Button>
              </Link>
            </CardFooter>
          </Card>
        ))}
      </div>
    </PageLayout>
  )
}

// Helper function to extract YouTube video ID from URL
function getYouTubeVideoId(url: string): string {
  if (!url) return ''
  // Handle different YouTube URL formats
  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/
  const match = url.match(regExp)
  return match && match[2].length === 11 ? match[2] : ''
}

'use client'

import { useEffect } from 'react'
import { But<PERSON> } from '@/components/Button'
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/Card'
import PageLayout from '@/components/PageLayout'

export default function ExercisesError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    console.error('Exercises section error:', error)
  }, [error])

  return (
    <PageLayout>
      <div className="flex items-center justify-center min-h-[60vh]">
        <Card className="w-full max-w-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
          <CardHeader>
            <CardTitle className="text-[#E63946]">
              Exercise Library Error
            </CardTitle>
          </CardHeader>
          <CardContent className="px-6 py-4">
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              An error occurred while loading the exercise library.
            </p>
            {error.message && (
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-md overflow-auto max-h-[200px] text-sm font-mono">
                {error.message}
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button onClick={reset} className="w-full max-w-xs">
              Try again
            </Button>
          </CardFooter>
        </Card>
      </div>
    </PageLayout>
  )
}

/* eslint-disable @typescript-eslint/no-explicit-any */
import * as supabaseSSR from '@supabase/ssr'
import * as nextHeaders from 'next/headers'
import * as nextServer from 'next/server'
import type { NextRequest } from 'next/server'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { GET } from './route'

vi.mock('@supabase/ssr')
vi.mock('next/headers')
vi.mock('next/server')

describe('/auth/callback route', () => {
  const exchangeCodeForSession = vi.fn()
  const getUser = vi.fn()
  const from = vi.fn()
  const select = vi.fn()
  const eq = vi.fn()
  const single = vi.fn()
  const redirect = vi.fn()
  const cookiesMock = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()

    // Setup mock chain for Supabase client
    single.mockResolvedValue({ data: { id: 'user-123' }, error: null })
    eq.mockReturnValue({ single })
    select.mockReturnValue({ eq })
    from.mockReturnValue({ select })
    ;(supabaseSSR as Record<string, unknown>).createServerClient = vi.fn(
      () => ({
        auth: {
          exchangeCodeForSession,
          getUser,
        },
        from,
      }),
    )
    ;(nextHeaders as Record<string, unknown>).cookies = cookiesMock
    ;(nextServer as Record<string, unknown>).NextResponse = { redirect }

    cookiesMock.mockResolvedValue({
      getAll: () => [],
      set: () => {},
    })

    getUser.mockResolvedValue({
      data: { user: { id: 'user-123' } },
      error: null,
    })
  })

  it('calls exchangeCodeForSession and redirects to /profile when successful', async () => {
    exchangeCodeForSession.mockResolvedValue({ error: null })

    const req = { url: 'https://site.com/auth/callback?code=abc' }
    await GET(req as NextRequest)

    expect(exchangeCodeForSession).toHaveBeenCalledWith('abc')
    expect(from).toHaveBeenCalledWith('profiles')
    expect(redirect).toHaveBeenCalledWith(new URL('/profile', req.url))
  })

  it('handles redirectTo parameter correctly', async () => {
    exchangeCodeForSession.mockResolvedValue({ error: null })

    const req = {
      url: 'https://site.com/auth/callback?code=abc&redirectTo=/dashboard',
    }
    await GET(req as NextRequest)

    expect(exchangeCodeForSession).toHaveBeenCalledWith('abc')
    expect(redirect).toHaveBeenCalledWith(new URL('/dashboard', req.url))
  })

  it('sanitizes malicious redirectTo parameter', async () => {
    exchangeCodeForSession.mockResolvedValue({ error: null })

    const req = {
      url: 'https://site.com/auth/callback?code=abc&redirectTo=https://evil.com',
    }
    await GET(req as NextRequest)

    expect(exchangeCodeForSession).toHaveBeenCalledWith('abc')
    expect(redirect).toHaveBeenCalledWith(new URL('/profile', req.url))
  })

  it('redirects to profile creation when user has no profile', async () => {
    exchangeCodeForSession.mockResolvedValue({ error: null })
    single.mockResolvedValue({ data: null, error: null })

    const req = { url: 'https://site.com/auth/callback?code=abc' }
    await GET(req as NextRequest)

    expect(redirect).toHaveBeenCalledWith(new URL('/profile/create', req.url))
  })

  it('handles auth errors by redirecting to login with error params', async () => {
    const req = {
      url: 'https://site.com/auth/callback?error=access_denied&error_description=User+cancelled',
    }
    await GET(req as NextRequest)

    expect(exchangeCodeForSession).not.toHaveBeenCalled()
    expect(redirect).toHaveBeenCalledWith(
      new URL(
        '/login?error=access_denied&error_description=User+cancelled',
        req.url,
      ),
    )
  })

  it('handles exchange code failure', async () => {
    exchangeCodeForSession.mockResolvedValue({
      error: { message: 'Invalid code' },
    })

    const req = { url: 'https://site.com/auth/callback?code=invalid' }
    await GET(req as NextRequest)

    expect(exchangeCodeForSession).toHaveBeenCalledWith('invalid')
    expect(redirect).toHaveBeenCalledWith(
      new URL(
        '/login?error=auth_code_exchange_failed&error_description=Failed+to+complete+authentication.+Please+try+again.',
        req.url,
      ),
    )
  })

  it('redirects to /profile even if no code', async () => {
    const req = { url: 'https://site.com/auth/callback' }
    await GET(req as NextRequest)

    expect(exchangeCodeForSession).not.toHaveBeenCalled()
    expect(redirect).toHaveBeenCalledWith(new URL('/profile', req.url))
  })
})

import { getSupabaseCredentials } from '@/lib/supabase/credentials'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { type NextRequest, NextResponse } from 'next/server'

/**
 * Route handler for Supabase OAuth/email confirmation callback.
 * Exchanges the code for a session and redirects to the appropriate page.
 * Includes proper error handling and secure redirect validation.
 */
export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')
  const error = requestUrl.searchParams.get('error')
  const errorDescription = requestUrl.searchParams.get('error_description')
  const redirectTo = requestUrl.searchParams.get('redirectTo') || '/profile'

  // Sanitize redirect URL to prevent open redirects
  const sanitizedRedirectTo = redirectTo.startsWith('/')
    ? redirectTo
    : '/profile'

  // Handle auth errors (e.g., expired link, invalid token)
  if (error) {
    console.error('Auth callback error:', error, errorDescription)
    const errorUrl = new URL('/login', request.url)
    errorUrl.searchParams.set('error', error)
    if (errorDescription) {
      errorUrl.searchParams.set('error_description', errorDescription)
    }
    return NextResponse.redirect(errorUrl)
  }

  if (code) {
    try {
      const cookieStore = await cookies()
      const { supabaseUrl, supabaseAnonKey } = getSupabaseCredentials()
      const supabase = createServerClient(supabaseUrl, supabaseAnonKey, {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            for (const { name, value, options } of cookiesToSet) {
              cookieStore.set({ name, value, ...options })
            }
          },
        },
      })

      const { error: exchangeError } =
        await supabase.auth.exchangeCodeForSession(code)

      if (exchangeError) {
        console.error('Failed to exchange code for session:', exchangeError)
        const errorUrl = new URL('/login', request.url)
        errorUrl.searchParams.set('error', 'auth_code_exchange_failed')
        errorUrl.searchParams.set(
          'error_description',
          'Failed to complete authentication. Please try again.',
        )
        return NextResponse.redirect(errorUrl)
      }

      // Check if user has a profile (in case the trigger failed)
      const {
        data: { user },
      } = await supabase.auth.getUser()
      if (user) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('id')
          .eq('id', user.id)
          .single()

        if (!profile) {
          console.warn(
            'User profile not found, redirecting to profile creation',
          )
          return NextResponse.redirect(new URL('/profile/create', request.url))
        }
      }
    } catch (error) {
      console.error('Unexpected error in auth callback:', error)
      const errorUrl = new URL('/login', request.url)
      errorUrl.searchParams.set('error', 'unexpected_error')
      errorUrl.searchParams.set(
        'error_description',
        'An unexpected error occurred during authentication.',
      )
      return NextResponse.redirect(errorUrl)
    }
  }

  // Successful authentication - redirect to the intended destination
  return NextResponse.redirect(new URL(sanitizedRedirectTo, request.url))
}

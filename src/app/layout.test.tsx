import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom/vitest'
import * as React from 'react'
import RootLayout from './layout'

// --- Mocks ---
// Mock ThemeProvider to simply render children
vi.mock('@/components/providers/theme-provider', () => ({
  ThemeProvider: ({ children }: { children: React.ReactNode }) => (
    <>{children}</>
  ),
}))

// Mock AuthProvider
vi.mock('@/contexts/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => (
    <>{children}</>
  ),
  useAuth: vi.fn(() => ({ user: null, loading: false, signOut: vi.fn() })), // Mock useAuth used by Navbar
}))

// Mock Navbar to avoid its complexities
vi.mock('@/widgets/Navbar', () => ({
  Navbar: () => <nav data-testid="navbar-mock">Navbar</nav>,
}))

// Mock Footer
vi.mock('@/components/Footer', () => ({
  Footer: () => <footer data-testid="footer-mock">Footer</footer>,
}))

// Mock PWAInstallPrompt
vi.mock('@/components/PWAInstallPrompt', () => ({
  PWAInstallPrompt: () => (
    <div data-testid="pwa-prompt-mock">PWAInstallPrompt</div>
  ),
}))

// Mock Toaster (actual component being tested for presence)
vi.mock('@/components/ui/sonner', () => ({
  Toaster: () => <div data-testid="toaster-mock">Toaster</div>,
}))

// Mock next/font
vi.mock('next/font/google', () => ({
  Oswald: () => ({
    style: { fontFamily: 'mock-oswald' },
    variable: '--font-heading',
    className: 'mock-oswald-classname',
  }),
}))
// --- End Mocks ---

describe('RootLayout Component', () => {
  it('should render its children', () => {
    render(
      <RootLayout>
        <div data-testid="child-content">Child Content</div>
      </RootLayout>,
    )
    expect(screen.getByTestId('child-content')).toBeTruthy()
  })

  it('should render the mocked Navbar', () => {
    render(<RootLayout>Children</RootLayout>)
    expect(screen.getByTestId('navbar-mock')).toBeTruthy()
  })

  it('should render the mocked Footer', () => {
    render(<RootLayout>Children</RootLayout>)
    expect(screen.getByTestId('footer-mock')).toBeTruthy()
  })

  it('should render the mocked PWAInstallPrompt', () => {
    render(<RootLayout>Children</RootLayout>)
    expect(screen.getByTestId('pwa-prompt-mock')).toBeTruthy()
  })

  // The actual test for the Toaster component
  it('should render the Toaster component (mocked)', () => {
    render(<RootLayout>Children</RootLayout>)
    const toasterMock = screen.getByTestId('toaster-mock')
    expect(toasterMock).toBeTruthy()
    expect(toasterMock.textContent).toBe('Toaster')
  })
})

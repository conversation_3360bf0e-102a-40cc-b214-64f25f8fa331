import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import { getSupabaseRouteHandlerClient } from '@/lib/supabase/server'
import EditProfileForm from '@/features/profile/EditProfileForm'

/**
 * /account/edit - Protected Server Component for editing user profile.
 * Fetches current user and profile, passes initial values to EditProfileForm.
 */
export default async function EditAccountPage() {
  const cookieStore = await cookies()
  const supabase = getSupabaseRouteHandlerClient(cookieStore)

  // Get authenticated user
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    // Should never happen due to middleware, but fallback
    redirect('/login')
  }

  // Fetch profile by auth user id
  const { data: profile } = await supabase
    .from('profiles')
    .select(
      'id, username, full_name, country, gender, weight_category, avatar_url, titles, social_links',
    )
    .eq('id', user.id)
    .single()

  if (!profile) {
    // If profile is missing, redirect to account page
    redirect('/account')
  }

  // Pass initial values to the client form
  return (
    <div className="container mx-auto max-w-2xl px-4 py-8">
      <h1 className="mb-6 text-3xl font-bold">Edit Profile</h1>
      <EditProfileForm initialProfile={profile} />
    </div>
  )
}

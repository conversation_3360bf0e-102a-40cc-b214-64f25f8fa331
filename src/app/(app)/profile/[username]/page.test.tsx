import { render } from '@testing-library/react'
import { notFound } from 'next/navigation'
import { beforeEach, describe, expect, it, vi } from 'vitest'

/* ------------------------------------------------------------------
 * GLOBAL MOCKS
 * ------------------------------------------------------------------ */

// 1) next/headers – we only need an empty cookies helper
vi.mock('next/headers', () => ({
  cookies: () => ({ getAll: () => [] }),
}))

// 2) Supabase – **central, complete mock** --------------------------
/*  The component under test calls
 *      getSupabaseRouteHandlerClient(cookieStore)
 *      .from("profiles") … .single()
 *  We stub the full chain and expose `mockSingle`
 *  so each test can decide what the DB should return.
 */
const mockSingle = vi.fn()
vi.mock('@/lib/supabase', () => ({
  __esModule: true,
  // Minimal stub for what the component uses
  getSupabaseRouteHandlerClient: () => ({
    from: () => ({
      select: () => ({
        eq: () => ({ single: mockSingle }),
      }),
    }),
  }),
}))

// 3) next/navigation – notFound must be spied on
vi.mock('next/navigation', () => ({ notFound: vi.fn() }))

// Component under test (import *after* mocks)
import PublicProfilePage from './page'

/* ------------------------------------------------------------------
 * TESTS
 * ------------------------------------------------------------------ */

describe('PublicProfilePage', () => {
  // Reset spies between cases
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('calls notFound if profile is missing', async () => {
    mockSingle.mockResolvedValue({
      data: null,
      error: { message: 'not found' },
    })

    const element = await PublicProfilePage({
      params: { username: 'nouser' },
    })
    render(element)

    expect(notFound).toHaveBeenCalled()
  })

  it('renders public info and badges, but not email', async () => {
    mockSingle.mockResolvedValue({
      data: {
        id: 'p1',
        username: 'publicuser',
        full_name: 'Public User',
        country: 'USA',
        gender: 'female',
        weight_category: '70kg',
        avatar_url: null,
        titles: ['Champion', 'MVP'],
      },
      error: null,
    })

    const element = await PublicProfilePage({
      params: { username: 'publicuser' },
    })
    const { findByText, queryByText } = render(element)

    expect(await findByText('Public User')).toBeInTheDocument()
    expect(await findByText('publicuser')).toBeInTheDocument()
    expect(await findByText('Champion')).toBeInTheDocument()
    expect(await findByText('MVP')).toBeInTheDocument()
    expect(queryByText(/email/i)).not.toBeInTheDocument()
  })
})

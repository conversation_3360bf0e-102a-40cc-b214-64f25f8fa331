import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { getSupabaseRouteHandlerClient } from '@/lib/supabase'
import { cookies } from 'next/headers'
import { notFound } from 'next/navigation'

type Profile = {
  id: string
  username: string
  full_name: string | null
  country: string | null
  gender: string | null
  weight_category: string | null
  avatar_url: string | null
  titles: string[] | null
}

export default async function PublicProfilePage({
  params,
}: {
  params: { username: string }
}) {
  const cookieStore = await cookies()
  const supabase = getSupabaseRouteHandlerClient(cookieStore)

  const uname = params.username.toLowerCase()

  const { data: profile, error } = await supabase
    .from('profiles')
    .select(
      'id, username, full_name, country, gender, weight_category, avatar_url, titles',
    )
    .eq('username', uname)
    .single<Profile>()

  if (error || !profile) {
    notFound()
    return null
  }

  return (
    <div className="container mx-auto max-w-2xl px-4 py-8">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>
            <div className="flex items-center gap-4">
              {profile.avatar_url ? (
                <img
                  src={profile.avatar_url}
                  alt="Avatar"
                  className="h-16 w-16 rounded-full border object-cover"
                />
              ) : (
                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gray-200 text-2xl font-bold">
                  {profile.username[0]?.toUpperCase() ?? '?'}
                </div>
              )}
              <span className="text-2xl font-bold">
                {profile.full_name ?? profile.username}
              </span>
            </div>
            {profile.titles && profile.titles.length > 0 && (
              <div className="mt-2 flex flex-wrap gap-1">
                {profile.titles.map((title) => (
                  <Badge key={title}>{title}</Badge>
                ))}
              </div>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <span className="font-medium">Username:</span> {profile.username}
            </div>
            <div>
              <span className="font-medium">Country:</span>{' '}
              {profile.country ?? <span className="text-gray-400">—</span>}
            </div>
            <div>
              <span className="font-medium">Gender:</span>{' '}
              {profile.gender ?? <span className="text-gray-400">—</span>}
            </div>
            <div>
              <span className="font-medium">Weight Category:</span>{' '}
              {profile.weight_category ?? (
                <span className="text-gray-400">—</span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Achievements</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-gray-500">
            Coming soon: Points, Medals, and Rank will be displayed here.
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Submission History</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-gray-500">
            Coming soon: Public submission history will appear here.
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

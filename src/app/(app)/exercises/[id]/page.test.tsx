import { render } from '@testing-library/react'
import { notFound } from 'next/navigation'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import ExerciseDetailPage from './page'

// Mock Next.js navigation
vi.mock('next/navigation', () => ({
  notFound: vi.fn(),
}))

// Mock VideoPlayer component
vi.mock('@/shared/ui/VideoPlayer', () => ({
  VideoPlayer: ({ url }: { url: string }) => (
    <div data-testid="video-player">{url}</div>
  ),
}))

// Mock GoToSubmitButton component
vi.mock('@/features/submissions/GoToSubmitButton', () => ({
  GoToSubmitButton: ({
    exerciseId,
    exerciseTitle,
  }: {
    exerciseId: string
    exerciseTitle: string
  }) => (
    <button data-testid="submit-button">
      Submit {exerciseTitle} (ID: {exerciseId})
    </button>
  ),
}))

// Mock Supabase client
vi.mock('@/lib/supabase/server', () => ({
  getSupabaseRouteHandlerClient: vi.fn(() => ({
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(() =>
            Promise.resolve({
              data: null,
              error: null,
            }),
          ),
        })),
      })),
    })),
  })),
}))

// Mock cookies
vi.mock('next/headers', () => ({
  cookies: vi.fn(() =>
    Promise.resolve({
      getAll: vi.fn(() => []),
    }),
  ),
}))

describe('ExerciseDetailPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('calls notFound() when exercise is not found', async () => {
    // Mock Supabase response for non-existent exercise
    const mockSupabase =
      require('@/lib/supabase/server').getSupabaseRouteHandlerClient()
    const mockFrom = mockSupabase.from
    const mockSelect = mockFrom().select
    const mockEq = mockSelect().eq
    const mockSingle = mockEq().single

    mockSingle.mockResolvedValue({
      data: null,
      error: { message: 'Exercise not found' },
    })

    // Render the page
    await ExerciseDetailPage({ params: { id: 'non-existent-id' } })

    // Check if notFound was called
    expect(notFound).toHaveBeenCalled()
  })

  it('renders exercise details when exercise is found', async () => {
    // Mock Supabase response for existing exercise
    const mockExercise = {
      id: '123',
      title: 'Test Exercise',
      description: 'Test Description',
      video_tutorial_url: 'https://www.youtube.com/watch?v=test',
      equipment_required: ['Equipment 1', 'Equipment 2'],
      evaluation_criteria: 'Test Criteria',
      medal_thresholds: {
        category_1: {
          bronze: 10,
          silver: 20,
          gold: 30,
        },
      },
      created_by: 'user-123',
      created_at: '2023-01-01',
      updated_at: '2023-01-01',
    }

    const mockSupabase =
      require('@/lib/supabase/server').getSupabaseRouteHandlerClient()
    const mockFrom = mockSupabase.from
    const mockSelect = mockFrom().select
    const mockEq = mockSelect().eq
    const mockSingle = mockEq().single

    mockSingle.mockResolvedValue({
      data: mockExercise,
      error: null,
    })

    // Render the page
    const { getByText, getByTestId } = render(
      await ExerciseDetailPage({ params: { id: '123' } }),
    )

    // Check if exercise details are rendered
    expect(getByText('Test Exercise')).toBeInTheDocument()
    expect(getByText('Test Description')).toBeInTheDocument()
    expect(getByText('Test Criteria')).toBeInTheDocument()
    expect(getByText('Equipment 1')).toBeInTheDocument()
    expect(getByText('Equipment 2')).toBeInTheDocument()

    // Check if VideoPlayer is rendered with correct URL
    expect(getByTestId('video-player').textContent).toBe(
      'https://www.youtube.com/watch?v=test',
    )

    // Check if GoToSubmitButton is rendered with correct props
    expect(getByTestId('submit-button').textContent).toBe(
      'Submit Test Exercise (ID: 123)',
    )
  })
})

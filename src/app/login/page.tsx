'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/contexts/AuthContext'

/**
 * Login page that redirects authenticated users to home
 * and shows a login button for unauthenticated users
 */
export default function LoginPage() {
  const router = useRouter()
  const { user, loading } = useAuth()

  // If user is already authenticated, redirect to home
  useEffect(() => {
    if (!loading && user) {
      router.replace('/')
    }
  }, [user, loading, router])

  // Handle login button click - redirect to home with query param to open auth dialog
  const handleLoginClick = () => {
    router.push('/?auth=signin')
  }

  if (loading) {
    return (
      <div className="flex min-h-[70vh] items-center justify-center">
        <div className="h-12 w-12 animate-spin rounded-full border-t-2 border-b-2 border-primary"></div>
      </div>
    )
  }

  // Only show login UI if user is not authenticated
  if (!user) {
    return (
      <div className="container mx-auto flex min-h-[70vh] flex-col items-center justify-center px-4 py-16">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-center text-2xl">Sign In</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col items-center">
            <p className="mb-6 text-center text-gray-600 dark:text-gray-300">
              Please sign in to access your account and submit performances.
            </p>
            <Button onClick={handleLoginClick} size="lg">
              Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // This should never render due to the redirect in useEffect
  return null
}

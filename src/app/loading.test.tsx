import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom/vitest'
import * as React from 'react'
import Loading from './loading'

// Mock the actual LoadingPage component
vi.mock('@/components/ui/loading-spinner', () => ({
  LoadingPage: () => <div data-testid="loading-page-mock">Loading...</div>,
}))

describe('Root Loading Component (app/loading.tsx)', () => {
  it('should render the LoadingPage component (mocked)', () => {
    render(<Loading />)
    const loadingMock = screen.getByTestId('loading-page-mock')
    expect(loadingMock).toBeInTheDocument()
    expect(loadingMock).toHaveTextContent('Loading...')
  })
})

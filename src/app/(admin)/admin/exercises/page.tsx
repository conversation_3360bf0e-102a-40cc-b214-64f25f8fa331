'use client' // Convert to Client Component for state and handlers

import * as React from 'react'
import { useState } from 'react'
import { notFound } from 'next/navigation' // Keep for admin check
// import { getSupabaseRouteHandlerClient } from '@/lib/supabase/server'; // Use actions now
import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
  DialogClose,
} from '@/components/ui/dialog'
import { toast } from 'sonner'

// Import Actions and Modal
import { deleteExercise } from '@/features/exercises/actions'
import { ExerciseModalForm } from '@/features/exercises/ExerciseModalForm'
import { type ExerciseFormData } from '@/features/exercises/ExerciseForm'

// Define Exercise type for fetched data
type Exercise = {
  id: string
  title: string
  description?: string
  video_tutorial_url?: string
  equipment_required?: string | string[] // Allow both array (DB) and string (form)
  evaluation_criteria?: string
  medal_thresholds?: string | Record<string, unknown> // Allow both string (form) and object (DB)
  created_at: string
}

interface AdminExercisesPageClientProps {
  // Renamed props interface
  exercises: Exercise[]
  isAdmin: boolean
}

// Renamed component
export default function AdminExercisesPageClient({
  exercises: initialExercises,
  isAdmin,
}: AdminExercisesPageClientProps) {
  if (!isAdmin) {
    notFound()
  }

  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingExercise, setEditingExercise] =
    useState<Partial<ExerciseFormData> | null>(null)
  const [deletingExerciseId, setDeletingExerciseId] = useState<string | null>(
    null,
  )
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [exercises, setExercises] = useState<Exercise[]>(initialExercises)

  const handleOpenCreateModal = () => {
    setEditingExercise(null)
    setIsModalOpen(true)
  }

  const handleOpenEditModal = (exercise: Exercise) => {
    const formData: Partial<ExerciseFormData> = {
      ...exercise,
      medal_thresholds:
        typeof exercise.medal_thresholds === 'object'
          ? JSON.stringify(exercise.medal_thresholds)
          : (exercise.medal_thresholds ??
            '{ "bronze": 0, "silver": 0, "gold": 0 }'),
      equipment_required: Array.isArray(exercise.equipment_required)
        ? exercise.equipment_required.join(', ')
        : typeof exercise.equipment_required === 'string'
          ? exercise.equipment_required
          : '',
    }
    setEditingExercise(formData)
    setIsModalOpen(true)
  }

  const handleOpenDeleteDialog = (id: string) => {
    setDeletingExerciseId(id)
    setIsDeleteDialogOpen(true)
  }

  const handleConfirmDelete = async () => {
    if (!deletingExerciseId) return

    setIsDeleting(true)
    const result = await deleteExercise(deletingExerciseId)
    setIsDeleting(false)
    setIsDeleteDialogOpen(false)

    if (result.success) {
      toast.success('Exercise deleted successfully!')
      setExercises((prev) => prev.filter((ex) => ex.id !== deletingExerciseId))
      setDeletingExerciseId(null)
    } else {
      toast.error(`Failed to delete exercise: ${result.error}`)
      setDeletingExerciseId(null)
    }
  }

  // Dummy onSubmit for ExerciseModalForm prop requirement
  const handleModalSubmit = async (data: ExerciseFormData) => {
    console.log('Modal submitted data:', data) // Placeholder
    // Actual submission is handled within ExerciseModalForm
    // This function could be used for post-submit actions in the page if needed
    return Promise.resolve()
  }

  return (
    <div className="container mx-auto py-10">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-2xl font-bold">Manage Exercises</h1>
        <Button onClick={handleOpenCreateModal}>Create New Exercise</Button>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Title</TableHead>
            <TableHead>Created At</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {exercises && exercises.length > 0 ? (
            exercises.map((exercise) => (
              <TableRow key={exercise.id}>
                <TableCell className="font-medium">{exercise.title}</TableCell>
                <TableCell>
                  {new Date(exercise.created_at).toLocaleDateString()}
                </TableCell>
                <TableCell className="text-right space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleOpenEditModal(exercise)}
                  >
                    Edit
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleOpenDeleteDialog(exercise.id)}
                  >
                    Delete
                  </Button>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={3} className="h-24 text-center">
                No exercises found.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      {/* Modal for Create/Edit - Add missing onSubmit prop */}
      <ExerciseModalForm
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
        initialData={editingExercise ?? undefined}
        onSubmit={handleModalSubmit} // Pass the handler
      />

      {/* Confirmation Dialog for Delete - Rebuilt using base Dialog components */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Are you absolutely sure?</DialogTitle>
            <DialogDescription>
              This action cannot be undone. This will permanently delete the
              exercise.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleConfirmDelete}
              disabled={isDeleting}
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Keep example Server Component wrapper commented out
/*
// src/app/(admin)/admin/exercises/page.tsx (Server Component)
// ... imports ...

async function checkAdminRoleServer() { ... }

export default async function AdminExercisesPageServer() { ... }
*/

/// <reference types="vitest" />

import * as AuthContext from '@/contexts/AuthContext'
import { renderHook } from '@testing-library/react'
import { usePathname } from 'next/navigation'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { useRoleAccess } from '../useRoleAccess'

// Module-level mock for next/navigation
vi.mock('next/navigation', () => ({
  usePathname: vi.fn(),
}))

describe('useRoleAccess', () => {
  const mockProfile = (role: string | undefined) => ({
    profile: role ? { role } : undefined,
  })

  beforeEach(() => {
    vi.restoreAllMocks()
    vi.mocked(usePathname).mockClear()
  })

  it('returns correct levels for admin on /profile', () => {
    vi.spyOn(AuthContext, 'useAuth').mockReturnValue(
      mockProfile('admin') as any,
    )
    vi.mocked(usePathname).mockReturnValue('/profile')
    const { result } = renderHook(() => useRoleAccess())
    expect(result.current).toEqual({ editLevel: 1, removeLevel: 1 })
  })

  it('returns correct levels for grandmaster on /exercises', () => {
    vi.spyOn(AuthContext, 'useAuth').mockReturnValue(
      mockProfile('grandmaster') as any,
    )
    vi.mocked(usePathname).mockReturnValue('/exercises')
    const { result } = renderHook(() => useRoleAccess())
    expect(result.current).toEqual({ editLevel: 1, removeLevel: 0 })
  })

  it('returns correct levels for athlete on /submissions', () => {
    vi.spyOn(AuthContext, 'useAuth').mockReturnValue(
      mockProfile('athlete') as any,
    )
    vi.mocked(usePathname).mockReturnValue('/submissions')
    const { result } = renderHook(() => useRoleAccess())
    expect(result.current).toEqual({ editLevel: 0, removeLevel: 0 })
  })

  it('returns {0,0} for missing role', () => {
    vi.spyOn(AuthContext, 'useAuth').mockReturnValue(
      mockProfile(undefined) as any,
    )
    vi.mocked(usePathname).mockReturnValue('/profile')
    const { result } = renderHook(() => useRoleAccess())
    expect(result.current).toEqual({ editLevel: 0, removeLevel: 0 })
  })

  it('returns correct levels when page and role are passed explicitly', () => {
    vi.spyOn(AuthContext, 'useAuth').mockReturnValue(
      mockProfile('athlete') as any,
    )
    vi.mocked(usePathname).mockReturnValue('/profile')
    const { result } = renderHook(() =>
      useRoleAccess({ page: '/exercises', role: 'grandmaster' }),
    )
    expect(result.current).toEqual({ editLevel: 1, removeLevel: 0 })
  })
})

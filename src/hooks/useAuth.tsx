import { UserRole } from '@/lib/permissions/types'
import { getSupabaseCredentials } from '@/lib/supabase/credentials'
import { createBrowserClient } from '@supabase/ssr'
import { Session, User as SupabaseUser } from '@supabase/supabase-js'
import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from 'react'

/**
 * Response type for authentication operations
 */
type AuthResponse = {
  error: string | null
  success: boolean
}

/**
 * Auth context type definition
 */
type AuthContextType = {
  user: SupabaseUser | null
  userRole: UserRole | null
  loading: boolean
  signOut: () => Promise<void>
  signInWithSocial: (
    provider: 'google' | 'facebook' | 'apple',
  ) => Promise<AuthResponse>
  signInWithEmail: (email: string, password: string) => Promise<AuthResponse>
  signUpWithEmail: (email: string, password: string) => Promise<AuthResponse>
  resetPassword: (email: string) => Promise<AuthResponse>
  updatePassword: (newPassword: string) => Promise<AuthResponse>

  // Legacy API compatibility (these now call the new methods internally)
  signIn: (
    email: string,
    password: string,
  ) => Promise<{ success: boolean; error?: string }>
  signUp: (
    email: string,
    password: string,
  ) => Promise<{ success: boolean; error?: string }>
  signInWithGoogle: () => Promise<{ success: boolean; error?: string }>
  signInWithFacebook: () => Promise<{ success: boolean; error?: string }>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<SupabaseUser | null>(null)
  const [userRole, setUserRole] = useState<UserRole | null>(null)
  const [loading, setLoading] = useState(true)

  const { supabaseUrl, supabaseAnonKey } = getSupabaseCredentials()
  const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey)

  // Fetch user role from profile
  const fetchUserRole = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error fetching user role:', error)
        return null
      }

      return (data?.role as UserRole) || null
    } catch (error) {
      console.error('Unexpected error fetching user role:', error)
      return null
    }
  }

  useEffect(() => {
    const getUser = async () => {
      setLoading(true)
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession()
        setUser(session?.user || null)

        if (session?.user) {
          const role = await fetchUserRole(session.user.id)
          setUserRole(role)
        } else {
          setUserRole(null)
        }
      } catch (error) {
        console.error('Error getting user session:', error)
      } finally {
        setLoading(false)
      }

      const { data: authListener } = supabase.auth.onAuthStateChange(
        async (event: string, session: Session | null) => {
          setUser(session?.user || null)

          if (session?.user) {
            const role = await fetchUserRole(session.user.id)
            setUserRole(role)
          } else {
            setUserRole(null)
          }
        },
      )

      return () => {
        authListener.subscription.unsubscribe()
      }
    }

    getUser()
  }, [supabase.auth])

  /**
   * Signs the user out
   */
  const signOut = async () => {
    await supabase.auth.signOut()
  }

  /**
   * Signs in with a social provider
   */
  const signInWithSocial = async (
    provider: 'google' | 'facebook' | 'apple',
  ): Promise<AuthResponse> => {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
        },
      })

      if (error) throw error
      return { error: null, success: true }
    } catch (error) {
      console.error(`${provider} auth error:`, error)
      return { error: (error as Error).message, success: false }
    }
  }

  /**
   * Signs in with email and password
   */
  const signInWithEmail = async (
    email: string,
    password: string,
  ): Promise<AuthResponse> => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })
      if (error) throw error
      return { error: null, success: true }
    } catch (error) {
      console.error('Email sign in error:', error)
      return { error: (error as Error).message, success: false }
    }
  }

  /**
   * Signs up with email and password
   */
  const signUpWithEmail = async (
    email: string,
    password: string,
  ): Promise<AuthResponse> => {
    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      })
      if (error) throw error
      return { error: null, success: true }
    } catch (error) {
      console.error('Email sign up error:', error)
      return { error: (error as Error).message, success: false }
    }
  }

  /**
   * Sends a password reset email
   */
  const resetPassword = async (email: string): Promise<AuthResponse> => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/update-password`,
      })

      if (error) throw error
      return { error: null, success: true }
    } catch (error) {
      console.error('Reset password error:', error)
      return { error: (error as Error).message, success: false }
    }
  }

  /**
   * Updates the user's password
   */
  const updatePassword = async (newPassword: string): Promise<AuthResponse> => {
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      })

      if (error) throw error
      return { error: null, success: true }
    } catch (error) {
      console.error('Update password error:', error)
      return { error: (error as Error).message, success: false }
    }
  }

  // Legacy API methods (for backward compatibility)
  // These now just call the new methods internally

  const signIn = async (email: string, password: string) => {
    const result = await signInWithEmail(email, password)
    return {
      success: result.success,
      error: result.error || undefined,
    }
  }

  const signUp = async (email: string, password: string) => {
    const result = await signUpWithEmail(email, password)
    return {
      success: result.success,
      error: result.error || undefined,
    }
  }

  const signInWithGoogle = async () => {
    const result = await signInWithSocial('google')
    return {
      success: result.success,
      error: result.error || undefined,
    }
  }

  const signInWithFacebook = async () => {
    const result = await signInWithSocial('facebook')
    return {
      success: result.success,
      error: result.error || undefined,
    }
  }

  const value = {
    user,
    userRole,
    loading,
    signOut,
    signInWithSocial,
    signInWithEmail,
    signUpWithEmail,
    resetPassword,
    updatePassword,
    // Legacy API
    signIn,
    signUp,
    signInWithGoogle,
    signInWithFacebook,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

// Hook to use the auth context
export function useAuth() {
  const context = useContext(AuthContext)

  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }

  return context
}

import { createServerClient } from '@/lib/supabase'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import { Permission, UserRole } from '@/lib/permissions/types'
import {
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
} from '@/lib/permissions'

/**
 * Interface for a user with role information from the database
 */
interface UserProfile {
  id: string
  auth_user_id: string
  role: UserRole
  [key: string]: any
}

/**
 * Get the current authenticated user with their role and profile information
 */
export async function getCurrentUser(): Promise<UserProfile | null> {
  const supabase = createServerClient()

  // Get the current user's session
  const {
    data: { session },
  } = await supabase.auth.getSession()

  if (!session?.user) {
    return null
  }

  // Get the user's profile with role
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('auth_user_id', session.user.id)
    .single<UserProfile>()

  return profile || null
}

/**
 * Check if the current user has specific permission, redirect if not
 * @param permission Permission to check
 * @param redirectUrl URL to redirect to if permission denied
 */
export async function requirePermission(
  permission: Permission,
  redirectUrl: string = '/auth/sign-in',
): Promise<UserProfile> {
  const user = await getCurrentUser()

  if (!user || !hasPermission(user, permission)) {
    redirect(redirectUrl)
  }

  return user
}

/**
 * Check if the current user has any of the specified permissions, redirect if not
 * @param permissions List of permissions to check
 * @param redirectUrl URL to redirect to if permission denied
 */
export async function requireAnyPermission(
  permissions: Permission[],
  redirectUrl: string = '/auth/sign-in',
): Promise<UserProfile> {
  const user = await getCurrentUser()

  if (!user || !hasAnyPermission(user, permissions)) {
    redirect(redirectUrl)
  }

  return user
}

/**
 * Check if the current user has all specified permissions, redirect if not
 * @param permissions List of permissions to check
 * @param redirectUrl URL to redirect to if permission denied
 */
export async function requireAllPermissions(
  permissions: Permission[],
  redirectUrl: string = '/auth/sign-in',
): Promise<UserProfile> {
  const user = await getCurrentUser()

  if (!user || !hasAllPermissions(user, permissions)) {
    redirect(redirectUrl)
  }

  return user
}

/**
 * Check if the current user has a specific role, redirect if not
 * @param role Role to check
 * @param redirectUrl URL to redirect to if role check fails
 */
export async function requireRole(
  role: UserRole,
  redirectUrl: string = '/auth/sign-in',
): Promise<UserProfile> {
  const user = await getCurrentUser()

  if (!user || user.role !== role) {
    redirect(redirectUrl)
  }

  return user
}

/**
 * Check if the current user has one of the specified roles, redirect if not
 * @param roles Roles to check
 * @param redirectUrl URL to redirect to if role check fails
 */
export async function requireAnyRole(
  roles: UserRole[],
  redirectUrl: string = '/auth/sign-in',
): Promise<UserProfile> {
  const user = await getCurrentUser()

  if (!user || !roles.includes(user.role)) {
    redirect(redirectUrl)
  }

  return user
}

/**
 * Check if current user is an administrator, redirect if not
 * @param redirectUrl URL to redirect to if check fails
 */
export async function requireAdmin(
  redirectUrl: string = '/auth/sign-in',
): Promise<UserProfile> {
  return requireRole('admin', redirectUrl)
}

/**
 * Check if current user is a grandmaster or admin, redirect if not
 * @param redirectUrl URL to redirect to if check fails
 */
export async function requireStaffRole(
  redirectUrl: string = '/auth/sign-in',
): Promise<UserProfile> {
  return requireAnyRole(['grandmaster', 'admin'], redirectUrl)
}

'use client'

import { useAuth } from '@/hooks/useAuth'
import type { UserRole } from '@/lib/permissions/types'
import { useRouter } from 'next/navigation'
import { type ReactNode, useEffect } from 'react'

/**
 * Props for the RoleGuard component
 */
interface RoleGuardProps {
  allowedRoles: UserRole[]
  children: ReactNode
  redirectTo?: string
}

/**
 * Component that restricts access based on user roles
 * If the user's role is not in the allowedRoles array, they are redirected
 */
export function RoleGuard({
  allowedRoles,
  children,
  redirectTo = '/unauthorized',
}: RoleGuardProps) {
  const { user, userRole, loading } = useAuth()
  const router = useRouter()

  // Show loading state while auth is being determined
  if (loading) {
    return null
  }

  // Redirect if user doesn't have permission (no useEffect needed)
  if (!user || !userRole || !allowedRoles.includes(userRole)) {
    router.push(redirectTo)
    return null
  }

  // User has permission, render children
  return <>{children}</>
}

/**
 * Route component that only allows athletes and higher roles
 */
export function AthleteRoute({
  children,
  redirectTo = '/unauthorized',
}: {
  children: ReactNode
  redirectTo?: string
}) {
  return (
    <RoleGuard
      allowedRoles={['athlete', 'grandmaster', 'admin']}
      redirectTo={redirectTo}
    >
      {children}
    </RoleGuard>
  )
}

/**
 * Route component that only allows grandmasters and admins
 */
export function GrandmasterRoute({
  children,
  redirectTo = '/unauthorized',
}: {
  children: ReactNode
  redirectTo?: string
}) {
  return (
    <RoleGuard allowedRoles={['grandmaster', 'admin']} redirectTo={redirectTo}>
      {children}
    </RoleGuard>
  )
}

/**
 * Route component that only allows admins
 */
export function AdminRoute({
  children,
  redirectTo = '/unauthorized',
}: {
  children: ReactNode
  redirectTo?: string
}) {
  return (
    <RoleGuard allowedRoles={['admin']} redirectTo={redirectTo}>
      {children}
    </RoleGuard>
  )
}

/**
 * Hook to check if a component should be rendered based on user role
 * @param requiredRoles Array of roles that are allowed to see the component
 * @returns boolean indicating if the component should be rendered
 */
export function useRoleGuard(requiredRoles: UserRole[]): boolean {
  const { userRole } = useAuth()

  if (!userRole) return false

  return requiredRoles.includes(userRole)
}

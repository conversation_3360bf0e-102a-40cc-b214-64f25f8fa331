import { supabase } from '@/lib/supabase/client'
import { UserWithRole } from '@/lib/permissions/permissions'

/**
 * Types of actions that can be logged
 */
export type AuditActionType =
  | 'role_change'
  | 'user_create'
  | 'user_delete'
  | 'exercise_create'
  | 'exercise_update'
  | 'exercise_delete'
  | 'submission_evaluate'
  | 'medal_award'
  | 'medal_revoke'
  | 'settings_update'

/**
 * Interface for audit log entry
 */
export interface AuditLogEntry {
  action_type: AuditActionType
  action_description: string
  target_user_id?: string
  target_resource_type?: string
  target_resource_id?: string
  previous_value?: Record<string, unknown>
  new_value?: Record<string, unknown>
}

/**
 * Creates an audit log entry in the database
 * @param user The user performing the action
 * @param logEntry The audit log entry details
 * @returns Promise resolving to success status
 */
export async function createAuditLog(
  user: UserWithRole,
  logEntry: AuditLogEntry,
): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase.from('audit_logs').insert({
      action_type: logEntry.action_type,
      action_description: logEntry.action_description,
      performed_by: user.id,
      target_user_id: logEntry.target_user_id,
      target_resource_type: logEntry.target_resource_type,
      target_resource_id: logEntry.target_resource_id,
      previous_value: logEntry.previous_value || null,
      new_value: logEntry.new_value || null,
      // Browser information
      user_agent: navigator.userAgent,
    })

    if (error) {
      console.error('Error creating audit log:', error)
      return { success: false, error: error.message }
    }

    return { success: true }
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error in createAuditLog:', errorMessage)
    return { success: false, error: errorMessage }
  }
}

/**
 * Logs a role change action
 * @param user The user performing the action
 * @param targetUserId The user whose role is being changed
 * @param oldRole The previous role
 * @param newRole The new role
 * @returns Promise resolving to success status
 */
export async function logRoleChange(
  user: UserWithRole,
  targetUserId: string,
  oldRole: string,
  newRole: string,
): Promise<{ success: boolean; error?: string }> {
  return createAuditLog(user, {
    action_type: 'role_change',
    action_description: `User role changed from ${oldRole} to ${newRole}`,
    target_user_id: targetUserId,
    target_resource_type: 'profile',
    target_resource_id: targetUserId,
    previous_value: { role: oldRole },
    new_value: { role: newRole },
  })
}

/**
 * Logs a user creation action
 * @param user The user performing the action
 * @param targetUserId The user being created
 * @param userData The user data
 * @returns Promise resolving to success status
 */
export async function logUserCreate(
  user: UserWithRole,
  targetUserId: string,
  userData: Record<string, unknown>,
): Promise<{ success: boolean; error?: string }> {
  return createAuditLog(user, {
    action_type: 'user_create',
    action_description: 'User account created',
    target_user_id: targetUserId,
    target_resource_type: 'profile',
    target_resource_id: targetUserId,
    new_value: userData,
  })
}

/**
 * Logs a user deletion action
 * @param user The user performing the action
 * @param targetUserId The user being deleted
 * @param userData The user data being deleted
 * @returns Promise resolving to success status
 */
export async function logUserDelete(
  user: UserWithRole,
  targetUserId: string,
  userData: Record<string, unknown>,
): Promise<{ success: boolean; error?: string }> {
  return createAuditLog(user, {
    action_type: 'user_delete',
    action_description: 'User account deleted',
    target_user_id: targetUserId,
    target_resource_type: 'profile',
    target_resource_id: targetUserId,
    previous_value: userData,
  })
}

/**
 * Logs an exercise creation action
 * @param user The user performing the action
 * @param exerciseId The exercise being created
 * @param exerciseData The exercise data
 * @returns Promise resolving to success status
 */
export async function logExerciseCreate(
  user: UserWithRole,
  exerciseId: string,
  exerciseData: Record<string, unknown>,
): Promise<{ success: boolean; error?: string }> {
  return createAuditLog(user, {
    action_type: 'exercise_create',
    action_description: 'Exercise created',
    target_resource_type: 'exercise',
    target_resource_id: exerciseId,
    new_value: exerciseData,
  })
}

/**
 * Logs an exercise update action
 * @param user The user performing the action
 * @param exerciseId The exercise being updated
 * @param oldData The previous exercise data
 * @param newData The new exercise data
 * @returns Promise resolving to success status
 */
export async function logExerciseUpdate(
  user: UserWithRole,
  exerciseId: string,
  oldData: Record<string, unknown>,
  newData: Record<string, unknown>,
): Promise<{ success: boolean; error?: string }> {
  return createAuditLog(user, {
    action_type: 'exercise_update',
    action_description: 'Exercise updated',
    target_resource_type: 'exercise',
    target_resource_id: exerciseId,
    previous_value: oldData,
    new_value: newData,
  })
}

/**
 * Logs an exercise deletion action
 * @param user The user performing the action
 * @param exerciseId The exercise being deleted
 * @param exerciseData The exercise data being deleted
 * @returns Promise resolving to success status
 */
export async function logExerciseDelete(
  user: UserWithRole,
  exerciseId: string,
  exerciseData: Record<string, unknown>,
): Promise<{ success: boolean; error?: string }> {
  return createAuditLog(user, {
    action_type: 'exercise_delete',
    action_description: 'Exercise deleted',
    target_resource_type: 'exercise',
    target_resource_id: exerciseId,
    previous_value: exerciseData,
  })
}

/**
 * Logs a submission evaluation action
 * @param user The user performing the action
 * @param submissionId The submission being evaluated
 * @param targetUserId The user who submitted the exercise
 * @param oldStatus The previous status
 * @param newStatus The new status
 * @param pointsAwarded The points awarded
 * @returns Promise resolving to success status
 */
export async function logSubmissionEvaluate(
  user: UserWithRole,
  submissionId: string,
  targetUserId: string,
  oldStatus: string,
  newStatus: string,
  pointsAwarded?: number,
): Promise<{ success: boolean; error?: string }> {
  return createAuditLog(user, {
    action_type: 'submission_evaluate',
    action_description: `Submission ${newStatus}`,
    target_user_id: targetUserId,
    target_resource_type: 'submission',
    target_resource_id: submissionId,
    previous_value: { status: oldStatus },
    new_value: {
      status: newStatus,
      points_awarded: pointsAwarded,
    },
  })
}

/**
 * Logs a medal award action
 * @param user The user performing the action
 * @param medalId The medal being awarded
 * @param targetUserId The user receiving the medal
 * @param medalType The type of medal
 * @returns Promise resolving to success status
 */
export async function logMedalAward(
  user: UserWithRole,
  medalId: string,
  targetUserId: string,
  medalType: string,
): Promise<{ success: boolean; error?: string }> {
  return createAuditLog(user, {
    action_type: 'medal_award',
    action_description: `${medalType} medal awarded`,
    target_user_id: targetUserId,
    target_resource_type: 'medal',
    target_resource_id: medalId,
    new_value: { medal_type: medalType },
  })
}

import { describe, expect, it } from 'vitest'
import {
  type AuthErrorType,
  emailSchema,
  getAuthErrorMessage,
  isValidEmail,
  isValidPassword,
  passwordResetSchema,
  passwordSchema,
  signInSchema,
  signUpSchema,
  updatePasswordSchema,
} from '../auth'

describe('Auth Validation Schemas', () => {
  describe('emailSchema', () => {
    it('accepts valid email addresses', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ]

      for (const email of validEmails) {
        const result = emailSchema.safeParse(email)
        expect(result.success).toBe(true)
      }
    })

    it('rejects invalid email addresses', () => {
      const invalidEmails = [
        '',
        'invalid',
        'test@',
        '@domain.com',
        '<EMAIL>',
        'test@domain',
        `${'a'.repeat(250)}@domain.com`, // too long
      ]

      for (const email of invalidEmails) {
        const result = emailSchema.safeParse(email)
        expect(result.success).toBe(false)
      }
    })

    it('provides appropriate error messages', () => {
      const result1 = emailSchema.safeParse('')
      expect(result1.success).toBe(false)
      if (!result1.success) {
        expect(result1.error.errors[0].message).toBe('Email is required')
      }

      const result2 = emailSchema.safeParse('invalid')
      expect(result2.success).toBe(false)
      if (!result2.success) {
        expect(result2.error.errors[0].message).toBe(
          'Please enter a valid email address',
        )
      }
    })
  })

  describe('passwordSchema', () => {
    it('accepts valid passwords', () => {
      const validPasswords = [
        'Password123',
        'MySecurePass1',
        'Complex123Pass',
        `aB3${'x'.repeat(50)}`, // exactly meeting requirements
      ]

      for (const password of validPasswords) {
        const result = passwordSchema.safeParse(password)
        expect(result.success).toBe(true)
      }
    })

    it('rejects passwords that are too short', () => {
      const shortPasswords = ['1234567', 'Pass1', 'aB3']

      for (const password of shortPasswords) {
        const result = passwordSchema.safeParse(password)
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.errors[0].message).toBe(
            'Password must be at least 8 characters long',
          )
        }
      }
    })

    it('rejects passwords that are too long', () => {
      const longPassword = 'a'.repeat(129)
      const result = passwordSchema.safeParse(longPassword)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors[0].message).toBe('Password is too long')
      }
    })

    it('rejects passwords without required character types', () => {
      const invalidPasswords = [
        'lowercase123', // no uppercase
        'UPPERCASE123', // no lowercase
        'NoNumbers!', // no numbers
        'onlylowercase', // no uppercase or numbers
        'ONLYUPPERCASE', // no lowercase or numbers
        '12345678', // only numbers
      ]

      for (const password of invalidPasswords) {
        const result = passwordSchema.safeParse(password)
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.errors[0].message).toBe(
            'Password must contain at least one uppercase letter, one lowercase letter, and one number',
          )
        }
      }
    })
  })

  describe('signUpSchema', () => {
    it('validates both email and password together', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'ValidPassword123',
      }

      const result = signUpSchema.safeParse(validData)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data).toEqual(validData)
      }
    })

    it('returns first error when both fields are invalid', () => {
      const invalidData = {
        email: 'invalid',
        password: 'weak',
      }

      const result = signUpSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors.length).toBeGreaterThan(0)
        // Should contain errors for both fields
        const errorPaths = result.error.errors.map((e) => e.path[0])
        expect(errorPaths).toContain('email')
        expect(errorPaths).toContain('password')
      }
    })
  })

  describe('signInSchema', () => {
    it('validates email and accepts any non-empty password', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'any', // sign in is less strict on password
      }

      const result = signInSchema.safeParse(validData)
      expect(result.success).toBe(true)
    })

    it('requires password to be non-empty', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: '',
      }

      const result = signInSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors[0].message).toBe('Password is required')
      }
    })
  })

  describe('updatePasswordSchema', () => {
    it('validates matching passwords', () => {
      const validData = {
        password: 'NewPassword123',
        confirmPassword: 'NewPassword123',
      }

      const result = updatePasswordSchema.safeParse(validData)
      expect(result.success).toBe(true)
    })

    it('rejects non-matching passwords', () => {
      const invalidData = {
        password: 'NewPassword123',
        confirmPassword: 'DifferentPassword123',
      }

      const result = updatePasswordSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors[0].message).toBe("Passwords don't match")
        expect(result.error.errors[0].path).toEqual(['confirmPassword'])
      }
    })
  })
})

describe('Utility Functions', () => {
  describe('isValidEmail', () => {
    it('returns true for valid emails', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true)
      expect(isValidEmail('<EMAIL>')).toBe(true)
    })

    it('returns false for invalid emails', () => {
      expect(isValidEmail('invalid')).toBe(false)
      expect(isValidEmail('')).toBe(false)
      expect(isValidEmail('test@')).toBe(false)
    })
  })

  describe('isValidPassword', () => {
    it('returns true for valid passwords', () => {
      expect(isValidPassword('ValidPassword123')).toBe(true)
      expect(isValidPassword('MySecure1')).toBe(true)
    })

    it('returns false for invalid passwords', () => {
      expect(isValidPassword('weak')).toBe(false)
      expect(isValidPassword('NoNumbers')).toBe(false)
      expect(isValidPassword('nonumber123')).toBe(false)
    })
  })

  describe('getAuthErrorMessage', () => {
    it('identifies email already registered errors', () => {
      const error = { message: 'Email already registered' }
      const result = getAuthErrorMessage(error)

      expect(result.type).toBe('EMAIL_ALREADY_REGISTERED')
      expect(result.message).toBe(
        'An account with this email already exists. Please sign in instead.',
      )
    })

    it('identifies invalid credentials errors', () => {
      const error = { message: 'Invalid login credentials' }
      const result = getAuthErrorMessage(error)

      expect(result.type).toBe('INVALID_CREDENTIALS')
      expect(result.message).toBe(
        'Invalid email or password. Please check your credentials and try again.',
      )
    })

    it('identifies email not confirmed errors', () => {
      const error = { message: 'Email not confirmed' }
      const result = getAuthErrorMessage(error)

      expect(result.type).toBe('EMAIL_NOT_CONFIRMED')
      expect(result.message).toBe(
        'Please check your email and click the confirmation link before signing in.',
      )
    })

    it('identifies rate limiting errors', () => {
      const error = { message: 'Too many requests' }
      const result = getAuthErrorMessage(error)

      expect(result.type).toBe('RATE_LIMITED')
      expect(result.message).toBe(
        'Too many attempts. Please wait a moment before trying again.',
      )
    })

    it('handles unknown errors gracefully', () => {
      const error = { message: 'Some unexpected error' }
      const result = getAuthErrorMessage(error)

      expect(result.type).toBe('UNKNOWN_ERROR')
      expect(result.message).toBe('Some unexpected error')
    })

    it('handles errors without message property', () => {
      const error = {}
      const result = getAuthErrorMessage(error)

      expect(result.type).toBe('UNKNOWN_ERROR')
      expect(result.message).toBe(
        'An unexpected error occurred. Please try again.',
      )
    })

    it('handles null/undefined errors', () => {
      const result1 = getAuthErrorMessage(null)
      const result2 = getAuthErrorMessage(undefined)

      expect(result1.type).toBe('UNKNOWN_ERROR')
      expect(result2.type).toBe('UNKNOWN_ERROR')
    })
  })
})

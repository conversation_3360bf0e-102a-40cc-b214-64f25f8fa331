/**
 * This script tests the connection to the local Supabase instance.
 * Run it with: npx ts-node -r tsconfig-paths/register src/lib/supabase/test-connection.ts
 *
 * Note: You may need to install ts-node and tsconfig-paths first:
 * npm install --save-dev ts-node tsconfig-paths
 */

import supabase from '../supabase'

async function testSupabaseConnection() {
  console.log('Testing connection to Supabase...')

  try {
    // Test a simple query to the system schema
    const { data, error } = await supabase
      .from('_prisma_migrations')
      .select('*')
      .limit(1)

    if (error) {
      throw error
    }

    console.log('✅ Successfully connected to Supabase!')
    console.log('Response data:', data)

    // Test authentication system
    const { data: authData, error: authError } =
      await supabase.auth.getSession()

    if (authError) {
      throw authError
    }

    console.log('✅ Authentication system is working!')
    console.log('Session:', authData.session ? 'Active' : 'None')

    return true
  } catch (error) {
    console.error('❌ Failed to connect to Supabase:', error)
    console.log('\nTroubleshooting tips:')
    console.log(
      '1. Make sure your local Supabase instance is running (supabase start)',
    )
    console.log(
      '2. Check that your .env.local file has the correct URL and keys',
    )
    console.log('3. Verify that the Supabase URL is accessible in your browser')
    console.log(
      '4. Try restarting your Supabase instance (supabase stop && supabase start)',
    )

    return false
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testSupabaseConnection()
    .then((success) => {
      if (!success) {
        process.exit(1)
      }
    })
    .catch((error) => {
      console.error('Unexpected error:', error)
      process.exit(1)
    })
}

export default testSupabaseConnection

import { createBrowserClient } from '@supabase/ssr'
import { getSupabaseCredentials } from './credentials'

/**
 * Creates a Supabase client for use in the browser.
 * Automatically uses the correct URL and anon key based on the environment.
 * Throws descriptive errors if env vars are missing.
 */
export const createClient = () => {
  const { supabaseUrl, supabaseAnonKey } = getSupabaseCredentials()
  return createBrowserClient(supabaseUrl, supabaseAnonKey)
}

// Export a singleton instance for client components
export const supabase = createClient()

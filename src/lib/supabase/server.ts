import { createServerClient } from '@supabase/ssr'
import { type ReadonlyRequestCookies } from 'next/dist/server/web/spec-extension/adapters/request-cookies'
import {
  getSupabaseCredentials,
  getSupabaseServiceCredentials,
} from './credentials'

/**
 * Creates a Supabase client for use in server components.
 * This client doesn't have cookie handling capabilities.
 */
export const createClient = () => {
  // For test environment, use raw process.env to allow test env mutation
  if (process.env.NODE_ENV === 'test') {
    // In tests, always use the helper to ensure mocks are properly used
    const { supabaseUrl, supabaseAnonKey } = getSupabaseCredentials()
    return createServerClient(supabaseUrl, supabaseAnonKey, {
      cookies: {
        getAll() {
          return []
        },
        setAll() {},
      },
    })
  }
  const { supabaseUrl, supabaseAnonKey } = getSupabaseCredentials()
  return createServerClient(supabaseUrl, supabaseAnonKey, {
    cookies: {
      getAll() {
        return []
      },
      setAll() {},
    },
  })
}

/**
 * Creates a Supabase client for use in route handlers with cookie support.
 *
 * Usage in a route handler:
 * ```
 * import { cookies } from 'next/headers';
 * import { createServerClient } from '@supabase/ssr';
 * import { getSupabaseRouteHandlerClient } from '@/lib/supabase/server';
 *
 * export async function POST(request: Request) {
 *   const cookieStore = cookies();
 *   const supabase = getSupabaseRouteHandlerClient(cookieStore);
 *   // Use supabase client...
 * }
 * ```
 */
export const getSupabaseRouteHandlerClient = (
  cookieStore: ReadonlyRequestCookies,
) => {
  if (process.env.NODE_ENV === 'test') {
    // In tests, always use the helper to ensure mocks are properly used
    const { supabaseUrl, supabaseAnonKey } = getSupabaseCredentials()
    return createServerClient(supabaseUrl, supabaseAnonKey, {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(
          cookiesToSet: Array<{
            name: string
            value: string
            options?: Record<string, unknown>
          }>,
        ) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              options
                ? cookieStore.set({ name, value, ...options })
                : cookieStore.set({ name, value }),
            )
          } catch {}
        },
      },
    })
  }
  const { supabaseUrl, supabaseAnonKey } = getSupabaseCredentials()
  return createServerClient(supabaseUrl, supabaseAnonKey, {
    cookies: {
      getAll() {
        return cookieStore.getAll()
      },
      setAll(cookiesToSet) {
        try {
          cookiesToSet.forEach(({ name, value, options }) =>
            cookieStore.set({ name, value, ...options }),
          )
        } catch {}
      },
    },
  })
}

/**
 * Creates a Supabase admin client with the service role key.
 * This client has full access to the database without RLS restrictions.
 * IMPORTANT: Only use this on the server side and never expose it to the client.
 */
export const createAdminClient = () => {
  if (process.env.NODE_ENV === 'test') {
    // In tests, always use the helper to ensure mocks are properly used
    const { supabaseUrl, supabaseServiceRoleKey } =
      getSupabaseServiceCredentials()
    return createServerClient(supabaseUrl, supabaseServiceRoleKey, {
      cookies: {
        getAll() {
          return []
        },
        setAll() {},
      },
    })
  }
  const { supabaseUrl, supabaseServiceRoleKey } =
    getSupabaseServiceCredentials()
  return createServerClient(supabaseUrl, supabaseServiceRoleKey, {
    cookies: {
      getAll() {
        return []
      },
      setAll() {},
    },
  })
}

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createBrowserClient } from '@supabase/ssr'
import { createClient } from './client'

// Mock the Supabase SSR module
vi.mock('@supabase/ssr', () => ({
  createBrowserClient: vi.fn(),
}))

describe('Supabase Browser Client Utility (src/lib/supabase/client.ts)', () => {
  const originalEnv = { ...process.env }

  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks()
    // Restore original env vars (Vitest config sets test env)
    process.env = { ...originalEnv, ...process.env }
  })

  afterEach(() => {
    // Restore original env vars after each test
    process.env = originalEnv
  })

  it('should call createBrowserClient with local credentials when NEXT_PUBLIC_SUPABASE_ENV is local', () => {
    process.env.NEXT_PUBLIC_SUPABASE_ENV = 'local'
    createClient() // Call the function under test

    expect(createBrowserClient).toHaveBeenCalledTimes(1)
    expect(createBrowserClient).toHaveBeenCalledWith(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    )
  })

  it('should call createBrowserClient with production credentials when NEXT_PUBLIC_SUPABASE_ENV is not local', () => {
    process.env.NEXT_PUBLIC_SUPABASE_ENV = 'production'
    createClient() // Call the function under test

    expect(createBrowserClient).toHaveBeenCalledTimes(1)
    expect(createBrowserClient).toHaveBeenCalledWith(
      process.env.NEXT_PUBLIC_SUPABASE_URL_PROD,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_PROD,
    )
  })

  it('should default to production credentials if NEXT_PUBLIC_SUPABASE_ENV is undefined', () => {
    delete process.env.NEXT_PUBLIC_SUPABASE_ENV
    createClient() // Call the function under test

    // Note: The current implementation defaults to production if env var is not 'local'
    expect(createBrowserClient).toHaveBeenCalledTimes(1)
    expect(createBrowserClient).toHaveBeenCalledWith(
      process.env.NEXT_PUBLIC_SUPABASE_URL_PROD, // Expect Production URL
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_PROD, // Expect Production Key
    )
  })
})

describe('createClient (Browser)', () => {
  it('should call createBrowserClient with correct env variables', () => {
    // Mock environment variables for the test
    const originalEnv = process.env
    process.env = {
      ...originalEnv,
      NEXT_PUBLIC_SUPABASE_URL: 'http://test-url.com',
      NEXT_PUBLIC_SUPABASE_ANON_KEY: 'test-anon-key',
    }

    createClient()

    expect(createBrowserClient).toHaveBeenCalledWith(
      'http://test-url.com',
      'test-anon-key',
    )

    // Restore original environment variables
    process.env = originalEnv
  })

  it('should return the client instance created by createBrowserClient', () => {
    const mockClient = { mock: 'client' }
    vi.mocked(createBrowserClient).mockReturnValue(mockClient as any)

    const client = createClient()

    expect(client).toBe(mockClient)
  })

  it('should throw error if Supabase URL is missing', () => {
    const originalEnv = process.env
    process.env = {
      ...originalEnv,
      NEXT_PUBLIC_SUPABASE_URL: undefined,
      NEXT_PUBLIC_SUPABASE_ANON_KEY: 'test-anon-key',
    }

    expect(() => createClient()).toThrow('Missing env.NEXT_PUBLIC_SUPABASE_URL')

    process.env = originalEnv
  })

  it('should throw error if Supabase Anon Key is missing', () => {
    const originalEnv = process.env
    process.env = {
      ...originalEnv,
      NEXT_PUBLIC_SUPABASE_URL: 'http://test-url.com',
      NEXT_PUBLIC_SUPABASE_ANON_KEY: undefined,
    }

    expect(() => createClient()).toThrow(
      'Missing env.NEXT_PUBLIC_SUPABASE_ANON_KEY',
    )

    process.env = originalEnv
  })
})

import { createServerClient } from '@supabase/ssr'
import { getSupabaseCredentials } from './credentials'
import type { NextRequest, NextResponse } from 'next/server'

/**
 * Returns a Supabase client for use in Next.js middleware,
 * with correct cookie handling for SSR.
 */
export function createClient(request: NextRequest, response: NextResponse) {
  const { supabaseUrl, supabaseAnonKey } = getSupabaseCredentials()
  return createServerClient(supabaseUrl, supabaseAnonKey, {
    cookies: {
      getAll() {
        return request.cookies.getAll()
      },
      setAll(cookiesToSet) {
        cookiesToSet.forEach(({ name, value, options }) =>
          response.cookies.set(name, value, options),
        )
      },
    },
  })
}

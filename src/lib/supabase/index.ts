// Import from client file
import { supabase, createClient as createBrowser<PERSON>lient } from './client'

// Import from server file
import {
  createClient as createServerClient,
  getSupabaseRouteHandlerClient,
  createAdminClient,
} from './server'

// Import auth helpers
import * as authHelpers from './auth'

// Re-export with renamed functions to avoid conflicts
export {
  // Client-side exports
  supabase,
  createBrowserClient,
  // Server-side exports
  createServerClient,
  getSupabaseRouteHandlerClient,
  createAdminClient,
}

// Re-export all auth helpers
export const auth = authHelpers

// Export default client for easier imports
export default supabase

/**
 * Fine-grained page-action matrix for in-page permissions.
 * Each role maps to a set of pages, each with { edit, remove } levels.
 * Edit this file to change in-page access for each role and page.
 */

export const pagePermissions = {
  admin: {
    '/profile': { edit: 1, remove: 1 },
    '/submissions': { edit: 1, remove: 1 },
    '/exercises': { edit: 1, remove: 1 },
  },
  grandmaster: {
    '/profile': { edit: 1, remove: 0 },
    '/submissions': { edit: 1, remove: 1 },
    '/exercises': { edit: 1, remove: 0 },
  },
  athlete: {
    '/profile': { edit: 1, remove: 0 },
    '/submissions': { edit: 0, remove: 0 },
    '/exercises': { edit: 0, remove: 0 },
  },
} as const

/**
 * Returns the access levels for a given role and page.
 * If not found, returns { edit: 0, remove: 0 }.
 * @param role - user role
 * @param page - page pathname (should start with '/')
 */
export function getPageAccess(
  role: keyof typeof pagePermissions,
  page: string,
): { edit: number; remove: number } {
  const table = pagePermissions[role] as Record<
    string,
    { edit: number; remove: number }
  >
  return table[page] ?? { edit: 0, remove: 0 }
}

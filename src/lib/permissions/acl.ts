/**
 * Central RBAC Access Control List (ACL) for route-level permissions.
 * Edit this file to change which roles can access which route prefixes.
 */

export const roleAccessMap = {
  admin: [
    '/',
    '/admin',
    '/submissions',
    '/exercises',
    '/rankings',
    '/profile',
    '/account',
  ],
  grandmaster: [
    '/',
    '/submissions',
    '/exercises',
    '/rankings',
    '/profile',
    '/account',
  ],
  athlete: ['/', '/exercises', '/rankings', '/profile', '/account'],
} as const

/**
 * Checks if a given path is allowed for the specified role.
 * Matching is by "startsWith" for each allowed prefix.
 * @param role - user role
 * @param path - pathname to check (should start with '/')
 * @returns true if allowed, false otherwise
 */
export function isPathAllowed(
  role: keyof typeof roleAccessMap,
  path: string,
): boolean {
  return roleAccessMap[role].some((prefix) =>
    prefix === '/' ? path === '/' : path.startsWith(prefix),
  )
}

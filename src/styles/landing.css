      /* Basic Reset & Smooth Scroll */
      *,
      *::before,
      *::after {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      html {
        scroll-behavior: smooth; /* Enables smooth scrolling for anchor links */
        font-family: sans-serif; /* Basic font */
        background-color: var(--gradient-start);
      }

      /* Color Palette Variables - Gradient BG + Orange Accent */
      :root {
        /* Accent Color */
        --burnt-orange: #ff4500;
        --burnt-orange-hover: #cc3700; /* Darker orange for hover (20% darker) */

        /* Base Text & Background Element Colors */
        --ivory: #fafcf2;
        --dimmed-ivory: #d8dcd3; /* For footer text */

        /* Gradient Colors */
        --gradient-start: #6a0dad; /* Purple */
        --gradient-mid: #1e3a8a; /* Dark Blue */
        --gradient-end: #0d9488; /* Teal */

        /* Medal Colors (Unchanged) */
        --bronze: #cd7f32;
        --bronze-border: #a06426;
        --silver: #c0c0c0;
        --silver-border: #a0a0a0;
        --gold: #ffd700;
        --gold-border: #cfae00;
        --platinum: #e5e4e2;
        --platinum-border: #babbbd;
        --diamond: #b9f2ff;
        --diamond-border: #8cd0e0;
        --medal-text-dark: #333; /* Dark text for light medals */

        /* Functional Mapping */
        --bg-gradient: linear-gradient(
          135deg,
          var(--gradient-start) 0%,
          var(--gradient-mid) 50%,
          var(--gradient-end) 100%
        );
        --surface-color: rgba(0, 0, 0, 0.5);
        --primary-color: var(
          --burnt-orange
        ); /* Main accent (buttons, headers) */
        --secondary-color: var(--ivory); /* Main text color */
        --accent-bg-color: rgba(
          0,
          0,
          0,
          0.35
        ); /* Semi-transparent dark for panels */
        --accent-text-color: var(
          --ivory
        ); /* Text color on accent backgrounds */
        --placeholder-bg: rgba(
          0,
          0,
          0,
          0.45
        ); /* Darker semi-transparent for placeholders */
        --placeholder-text: var(--ivory); /* Placeholder text color */
        --border-color: rgba(
          255,
          255,
          255,
          0.2
        ); /* Semi-transparent white for borders */
        --footer-text-color: var(--dimmed-ivory);
        --button-hover-color: var(--burnt-orange-hover);
        --input-bg-color: rgba(
          0,
          0,
          0,
          0.25
        ); /* Semi-transparent dark input bg */

        /* Layout */
        --border-radius: 6px; /* Slightly less rounded corners */
      }

      body {
        background: var(--bg-gradient) no-repeat center center fixed;
        background-size: cover;
        color: var(--secondary-color);
        line-height: 1.6;
      }

      /* Container */
      .container {
        max-width: 1200px; /* Increased width */
        margin: 0 auto;
        padding: 0 25px; /* Adjusted padding */
      }

      /* Header Styles */
      .main-header {
        background-color: var(--surface-color);
        padding: 0.8rem 0; /* Adjusted padding */
        border-bottom: 1px solid var(--border-color);
        position: sticky;
        top: 0;
        z-index: 10;
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
      }

      .main-header .container {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .header-left {
        display: flex;
        align-items: center;
        gap: 15px;
      }

      /* ADDED: Remove default styles from logo/name links */
      .header-left a {
        text-decoration: none;
        color: inherit; /* Inherit color from parent */
        display: flex; /* Maintain flex alignment */
        align-items: center;
      }
      /* END ADDED */

      .logo-placeholder {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 80px; /* Adjusted size */
        height: 50px;
      }

      .platform-name {
        font-size: 2rem; /* Slightly smaller */
        font-weight: bold;
        color: var(--secondary-color);
      }

      /* NEW: Header Navigation */
      .main-header nav ul {
        list-style: none;
        display: flex;
        gap: 25px; /* Spacing between links */
      }

      .main-header nav a {
        color: var(--secondary-color);
        text-decoration: none;
        font-size: 0.95rem;
        transition: color 0.3s ease;
      }

      .main-header nav a:hover {
        color: var(--primary-color); /* Orange on hover */
      }
      /* END NEW: Header Navigation */

      /* Section Padding & Headings */
      section {
        padding: 70px 0; /* Increased padding */
      }

      section h2 {
        text-align: center;
        margin-bottom: 50px; /* Increased margin */
        font-size: 2.2rem; /* Slightly larger */
        color: var(--primary-color); /* Orange */
      }

      /* NEW: Subheadings */
      h3.section-subheading {
        text-align: center;
        font-size: 1.5rem;
        color: var(--secondary-color);
        margin-top: 40px;
        margin-bottom: 20px;
        font-weight: 600;
      }
      /* END NEW: Subheadings */

      /* Hero Section */
      .hero {
        text-align: center;
        padding: 120px 0 100px 0; /* Adjusted padding */
        color: var(--secondary-color);
      }

      .hero h1 {
        font-size: 3rem; /* Larger */
        margin-bottom: 1.5rem;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.4);
      }

      .hero p {
        font-size: 1.3rem; /* Slightly larger */
        max-width: 650px; /* Adjusted width */
        margin: 0 auto 2.5rem auto; /* Adjusted margin */
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
      }

      .cta-button {
        display: inline-block;
        background-color: var(--primary-color); /* Orange */
        color: var(--secondary-color); /* Ivory */
        padding: 14px 30px; /* Larger padding */
        text-decoration: none;
        font-weight: bold;
        border-radius: var(--border-radius); /* Use variable */
        transition: background-color 0.3s ease, transform 0.2s ease;
        font-size: 1.05rem; /* Slightly larger */
        border: none;
        cursor: pointer;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
      }

      .cta-button:hover {
        background-color: var(--button-hover-color); /* Darker Orange */
        transform: translateY(-2px);
      }

      .cta-button:focus {
        outline: 3px solid #fff;
        outline-offset: 2px;
      }

      /* How It Works Section */
      .how-it-works-grid {
        display: grid;
        grid-template-columns: repeat(
          auto-fit,
          minmax(220px, 1fr)
        ); /* Adjusted minmax */
        gap: 35px; /* Increased gap */
        text-align: center;
        justify-content: center; /* Added to center items when wrapping */
      }

      .step {
        background-color: var(--accent-bg-color);
        color: var(--secondary-color);
        padding: 30px; /* Increased padding */
        border-radius: var(--border-radius); /* Use variable */
        border: 1px solid var(--border-color);
        backdrop-filter: blur(3px);
        -webkit-backdrop-filter: blur(3px);
      }

      .step-icon {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        background-color: rgba(
          65,
          44,
          130,
          0.8
        ); /* Muted blue-purple instead of red */
        margin: 0 auto 20px auto;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        color: white;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        transition: transform 0.3s ease, background-color 0.3s ease;
        position: relative;
      }

      .step:hover .step-icon {
        transform: scale(1.1);
        background-color: rgba(65, 44, 130, 1); /* Full opacity on hover */
      }

      .step h3 {
        margin-bottom: 15px; /* Adjusted margin */
        font-size: 1.3rem; /* Larger */
        color: var(--primary-color); /* Orange */
      }

      /* Exercise Showcase Section */
      #exercises {
        background-color: var(--surface-color);
        border-radius: var(--border-radius); /* Use variable */
        margin-top: 50px; /* Adjusted margin */
        padding: 50px 0; /* Adjusted padding */
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
      }

      .exercise-showcase-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); /* Increased min width */
        gap: 30px; /* Adjusted gap */
      }

      .exercise-card {
        background-color: rgba(197, 143, 243, 0.21); /* New requested color #c58ff336 */
        color: var(--secondary-color);
        border-radius: var(--border-radius);
        overflow: hidden;
        border: 1px solid var(--border-color);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        display: flex;
        flex-direction: column;
        text-align: center;
        padding-bottom: 20px; /* Added padding to bottom */
      }

      .exercise-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
      }

      .exercise-img-container {
        height: 220px; /* Increased height */
        overflow: hidden;
        position: relative;
        /* Removed background-color: var(--placeholder-bg); as card has bg now */
      }

      .exercise-img {
        width: 100%;
        height: 100%;
        object-fit: contain; /* Changed from cover to contain */
        transition: transform 0.3s ease;
      }

      .exercise-card:hover .exercise-img {
        transform: scale(1.05);
      }

      .exercise-title {
        font-size: 1.2rem;
        color: var(--primary-color);
        margin: 15px 15px 0; /* Adjusted margin (removed bottom margin) */
        font-weight: 600;
      }


      .exercise-card.coming-soon .exercise-img-container {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .exercise-icon {
        font-size: 4rem;
        color: var(--primary-color);
        opacity: 0.8;
      }

      @media (max-width: 768px) {
        .exercise-img-container {
          height: 180px; /* Increased height */
        }
      }

      @media (max-width: 480px) {
        .exercise-img-container {
          height: 160px; /* Increased height */
        }
      }

      /* Medal Section */
      #medals {
        background: var(--surface-color);
        border-radius: var(--border-radius);
        margin-top: 50px;
        padding: 70px 0 80px 0; /* Adjusted vertical padding */
        /* color: var(--secondary-color); */
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
        position: relative;
        overflow: hidden;
      }

      /* Subtle background effect for medals section */
      #medals::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(
          circle,
          rgba(255, 255, 255, 0.1) 0%,
          rgba(255, 255, 255, 0) 70%
        );
        opacity: 0.4;
        z-index: 0;
      }

      #medals .container {
        position: relative;
        z-index: 1;
      }

      #medals h2 {
        color: #ff4500; /* Brighter orange for better contrast */
        font-size: 2.5rem; /* Larger heading */
        margin-bottom: 60px; /* More space below heading */
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2); /* Text shadow for depth */
      }

      /* UPDATED: Paragraph moved below medals */
      #medals .container .medals-description {
        max-width: 750px; /* Adjusted width */
        margin: 0 auto; /* Centered */
        text-align: center;
      }

      /* Medal Preview Styles */
      .medals-preview-grid {
        display: flex;
        justify-content: center;
        gap: 40px; /* Adjusted gap between medals */
        margin-bottom: 50px; /* Added margin below medals */
        flex-wrap: wrap;
      }

      .medal-placeholder {
        display: flex;
        justify-content: center;
        align-items: center;
        background: transparent;
        border: none;
        box-shadow: none;
        animation: medalPulse 3s infinite alternate;
        animation-delay: calc(var(--medal-index, 0) * 0.5s);
        position: relative;
      }

      /* Set animation delay for each medal */
      .medal-placeholder:nth-child(1) {
        --medal-index: 0;
      }
      .medal-placeholder:nth-child(2) {
        --medal-index: 1;
      }
      .medal-placeholder:nth-child(3) {
        --medal-index: 2;
      }
      .medal-placeholder:nth-child(4) {
        --medal-index: 3;
        --halo-color: rgba(229, 228, 226, 0.5); /* Platinum */
      }
      .medal-placeholder:nth-child(5) {
        --medal-index: 4;
        --halo-color: rgba(185, 242, 255, 0.5); /* Diamond */
      }

      /* Add halo effect for platinum and diamond */
      .medal-placeholder:nth-child(4)::before,
      .medal-placeholder:nth-child(5)::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 210px;
        height: 210px;
        transform: translate(-50%, -50%);
        border-radius: 50%;
        background: var(--halo-color);
        filter: blur(20px);
        z-index: -1;
        opacity: 0.3;
        transition: all 0.3s ease;
      }

      .medal-placeholder:nth-child(4):hover::before,
      .medal-placeholder:nth-child(5):hover::before {
        opacity: 0.8;
        width: 220px;
        height: 220px;
        filter: blur(25px);
      }

      @keyframes medalPulse {
        0% {
          transform: scale(1);
        }
        100% {
          transform: scale(1.05);
        }
      }

      .medal-img {
        width: 180px;
        height: 180px;
        object-fit: contain;
        transition: transform 0.3s ease, filter 0.3s ease;
      }

      .medal-placeholder:hover .medal-img {
        transform: scale(1.08);
        filter: drop-shadow(0 10px 25px rgba(0, 0, 0, 0.35));
      }

      @media (max-width: 768px) {
        .medal-img {
          width: 150px;
          height: 150px;
        }
        .medals-preview-grid {
          gap: 40px; /* Adjusted gap for medium screens */
        }
      }

      @media (max-width: 480px) {
        .medal-img {
          width: 120px;
          height: 120px;
        }
        .medals-preview-grid {
          gap: 25px; /* Adjusted gap for small screens */
        }
      }

      /* Grandmasters Section */
      #grandmasters {
        background-color: var(--surface-color);
        border-radius: var(--border-radius); /* Use variable */
        margin-top: 50px; /* Adjusted margin */
        padding: 50px 0; /* Adjusted padding */
        color: var(--secondary-color);
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
      }
      #grandmasters h2 {
        color: var(--primary-color); /* Orange */
      }

      /* UPDATED: Paragraph moved after subheading */
      #grandmasters .container .grandmasters-description {
        max-width: 750px; /* Adjusted width */
        margin: 0 auto 30px auto; /* Adjusted margin */
        text-align: center;
      }

      .grandmasters-grid {
        display: flex;
        justify-content: center;
        gap: 35px;
        margin-top: 0;
        flex-wrap: wrap;
      }

      .grandmaster-card {
        width: 300px;
        height: 400px;
        position: relative;
        border-radius: var(--border-radius);
        overflow: hidden;
        background-size: cover;
        background-position: center;
        transition: transform 0.3s ease;
      }

      .grandmaster-card:nth-child(1) {
        background-image: url('./images/grandmasters/slava.jpeg');
      }

      .grandmaster-card:nth-child(2) {
        background-image: url('./images/grandmasters/paulina.jpeg');
      }

      .card-content {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 20px;
        background: linear-gradient(
          to top,
          rgba(0, 0, 0, 0.9) 0%,
          rgba(0, 0, 0, 0.7) 60%,
          rgba(0, 0, 0, 0) 100%
        );
        color: var(--secondary-color);
        transform: translateY(70%);
        transition: transform 0.3s ease;
      }

      .grandmaster-card:hover {
        transform: scale(1.02);
      }

      .grandmaster-card:hover .card-content {
        transform: translateY(0);
      }

      .card-content h4 {
        font-size: 1.4rem;
        margin-bottom: 10px;
        color: var(--primary-color);
      }

      .card-content .title {
        font-size: 1rem;
        margin-bottom: 8px;
        font-weight: 600;
        min-height: 5rem; /* Ensure consistent height for title paragraph */
      }

      .card-content .credentials {
        font-size: 1rem;
        opacity: 0.9;
      }

      @media (max-width: 768px) {
        .grandmaster-card {
          width: 250px;
          height: 350px;
        }

        .card-content {
          transform: translateY(0);
          background: linear-gradient(
            to top,
            rgba(0, 0, 0, 0.9) 0%,
            rgba(0, 0, 0, 0.7) 100%
          );
        }
      }

      @media (max-width: 480px) {
        .grandmaster-card {
          width: 100%;
          height: 300px;
        }
      }

      /* Email Sign-up Form Section */
      #signup {
        padding-bottom: 80px; /* Add padding bottom */
      }

      .signup-form-container {
        max-width: 550px; /* Adjusted width */
        margin: 50px auto 0 auto; /* Adjusted margin */
        padding: 35px; /* Adjusted padding */
        background-color: var(--accent-bg-color);
        color: var(--secondary-color);
        border-radius: var(--border-radius); /* Use variable */
        text-align: center;
        border: 1px solid var(--border-color);
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
      }
      #signup h2 {
        color: var(--primary-color); /* Orange */
      }

      .signup-form-container p {
        margin-bottom: 25px; /* Adjusted margin */
        font-size: 1.3rem;
      }

      .signup-form label {
        display: block;
        margin-bottom: 10px; /* Adjusted margin */
        font-weight: bold;
        text-align: left;
        color: var(--secondary-color);
      }

      .signup-form input[type='email'] {
        width: 100%;
        padding: 14px; /* Adjusted padding */
        margin-bottom: 25px; /* Adjusted margin */
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius); /* Use variable */
        background-color: var(--input-bg-color);
        color: var(--secondary-color);
        font-size: 1rem;
      }
      .signup-form input[type='email']::placeholder {
        color: rgba(250, 252, 242, 0.6);
        opacity: 1;
      }

      .signup-form input[type='email']:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 5px rgba(191, 52, 10, 0.5); /* Orange shadow */
      }

      .form-message {
        margin-top: 15px;
        padding: 10px;
        border-radius: var(--border-radius);
        font-weight: 500;
        display: none;
      }

      .form-message.success {
        background-color: rgba(0, 128, 0, 0.2);
        color: #a0ffa0;
        border: 1px solid rgba(0, 128, 0, 0.3);
        display: block;
      }

      .form-message.error {
        background-color: rgba(255, 0, 0, 0.2);
        color: #ffa0a0;
        border: 1px solid rgba(255, 0, 0, 0.3);
        display: block;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      /* Footer Styles */
      .main-footer {
        background-color: var(--surface-color);
        color: var(--footer-text-color);
        text-align: center;
        padding: 1.8rem 0; /* Adjusted padding */
        margin-top: 60px;
        border-top: 1px solid var(--border-color);
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
      }

      /* Responsive Design */
      @media (max-width: 992px) {
        /* Adjusted breakpoint */
        .main-header nav {
          display: none; /* Hide nav on smaller screens */
        }
        .container {
          padding: 0 20px;
        }
      }

      @media (max-width: 768px) {
        .hero h1 {
          font-size: 2.2rem;
        }
        .hero p {
          font-size: 1rem;
          max-width: 90%;
        }
        section h2 {
          font-size: 1.8rem;
          margin-bottom: 40px;
        }
        h3.section-subheading {
          font-size: 1.3rem;
        }
        .main-header .container {
          flex-direction: row; /* Keep row for logo/name */
          justify-content: center;
        } /* Center header content */
        .platform-name {
          font-size: 1.2rem;
        }
        .medals-preview-grid {
          gap: 40px;
        }
        .medal-placeholder {
          width: 200px;
          height: 200px;
        }
        .medal-placeholder .medal-icon {
          width: 35px;
          height: 35px;
          font-size: 1.3em;
        }
        .grandmasters-grid {
          gap: 20px;
        }
        .grandmaster-placeholder {
          width: 160px;
          height: 160px;
        }
        .medal-img {
          width: 85px;
          height: 85px;
        }

        /* Medal section responsive styles */
        #medals h2 {
          font-size: 2.2rem;
          margin-bottom: 40px;
        }
      }

      @media (max-width: 480px) {
        .hero h1 {
          font-size: 1.8rem;
        }
        .hero p {
          font-size: 0.95rem;
        }
        .cta-button {
          padding: 12px 25px;
          font-size: 1rem;
        }
        .how-it-works-grid,
        .exercise-showcase-grid {
          grid-template-columns: 1fr;
          gap: 25px;
        }
        .step {
          padding: 25px;
        }
        .step h3 {
          font-size: 1.2rem;
        }
        .medals-preview-grid {
          gap: 25px;
        }
        .medal-placeholder {
          width: 100px;
          height: 100px;
        }
        .medal-placeholder .medal-icon {
          width: 30px;
          height: 30px;
          font-size: 1.2em;
        }
        .grandmasters-grid {
          gap: 15px;
        }
        .grandmaster-placeholder {
          width: 140px;
          height: 140px;
          font-size: 1rem;
        }
        .signup-form-container {
          padding: 25px;
        }
        .medal-img {
          width: 70px;
          height: 70px;
        }
        .medal-placeholder span {
          font-size: 0.9em;
        }

        /* Medal section responsive styles */
        #medals h2 {
          font-size: 1.8rem;
          margin-bottom: 30px;
        }
      }

      /* ADDED: Image Styles */
      .logo-img {
        height: 60px;
        width: auto;
      }

      .medal-img {
        width: 180px;
        height: 180px;
        object-fit: contain;
        transition: transform 0.3s ease, filter 0.3s ease;
      }

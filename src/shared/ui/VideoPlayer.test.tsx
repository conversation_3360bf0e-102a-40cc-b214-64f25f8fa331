import { render, screen } from '@testing-library/react'
import { VideoPlayer } from './VideoPlayer'
import { describe, it, expect } from 'vitest'

describe('VideoPlayer Component', () => {
  it('renders YouTube video correctly', () => {
    render(<VideoPlayer url="https://www.youtube.com/watch?v=dQw4w9WgXcQ" />)
    const iframe = screen.getByTitle('YouTube video player')
    expect(iframe).toBeInTheDocument()
    expect(iframe.getAttribute('src')).toContain('dQw4w9WgXcQ')
  })

  it('renders YouTube shortened URL correctly', () => {
    render(<VideoPlayer url="https://youtu.be/dQw4w9WgXcQ" />)
    const iframe = screen.getByTitle('YouTube video player')
    expect(iframe).toBeInTheDocument()
    expect(iframe.getAttribute('src')).toContain('dQw4w9WgXcQ')
  })

  it('renders YouTube embed URL correctly', () => {
    render(<VideoPlayer url="https://www.youtube.com/embed/dQw4w9WgXcQ" />)
    const iframe = screen.getByTitle('YouTube video player')
    expect(iframe).toBeInTheDocument()
    expect(iframe.getAttribute('src')).toContain('dQw4w9WgXcQ')
  })

  it('shows error state for invalid YouTube URL', () => {
    render(<VideoPlayer url="https://www.youtube.com/watch?v=invalid" />)
    expect(screen.getByText('Invalid YouTube URL')).toBeInTheDocument()
  })

  it('shows fallback for TikTok URL', () => {
    render(
      <VideoPlayer url="https://www.tiktok.com/@username/video/1234567890" />,
    )
    expect(
      screen.getByText('TikTok videos can be viewed directly on TikTok'),
    ).toBeInTheDocument()
    expect(screen.getByText('Open TikTok Video')).toBeInTheDocument()
  })

  it('shows fallback for Instagram URL', () => {
    render(<VideoPlayer url="https://www.instagram.com/p/abcdef123456/" />)
    expect(
      screen.getByText('Instagram posts can be viewed directly on Instagram'),
    ).toBeInTheDocument()
    expect(screen.getByText('Open Instagram Post')).toBeInTheDocument()
  })

  it('shows error state for unsupported platform', () => {
    render(<VideoPlayer url="https://www.example.com/video" />)
    expect(
      screen.getByText(
        'Unsupported video platform. Please use YouTube, TikTok, or Instagram.',
      ),
    ).toBeInTheDocument()
  })

  it('shows error state for empty URL', () => {
    render(<VideoPlayer url="" />)
    expect(screen.getByText('No video URL provided')).toBeInTheDocument()
  })
})

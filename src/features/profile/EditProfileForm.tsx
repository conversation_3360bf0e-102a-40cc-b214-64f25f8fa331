'use client'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { createClient } from '@/lib/supabase/client'
import { useForm } from '@tanstack/react-form'
import * as React from 'react'
import { toast } from 'sonner'
import { z } from 'zod/v4'
import { updateProfile } from './actions'
import {
  type EditProfileFormProps,
  type EditProfileFormValues,
  EditProfileSchema,
} from './profileSchema'

/**
 * EditProfileForm - Client component for editing user profile.
 * Uses TanStack Form's <form.Field> API with full typing and Zod validation.
 */
const EditProfileForm: React.FC<EditProfileFormProps> = ({
  initialProfile,
}) => {
  const initialValues: EditProfileFormValues = {
    full_name: initialProfile.full_name ?? '',
    username: initialProfile.username ?? '',
    country: initialProfile.country ?? '',
    gender: (initialProfile.gender as 'male' | 'female' | 'other') ?? 'male',
    weight_category:
      (initialProfile.weight_category as 'under_95kg' | 'over_95kg') ??
      'under_95kg',
    titles: initialProfile.titles?.join(', ') ?? '',
    social_links: initialProfile.social_links
      ? JSON.stringify(initialProfile.social_links)
      : '',
    avatar_url: initialProfile.avatar_url ?? '',
  }

  const [avatarUrl, setAvatarUrl] = React.useState(initialValues.avatar_url)
  const [uploading, setUploading] = React.useState(false)
  const supabase = React.useMemo(() => createClient(), [])

  async function handleAvatarChange(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0]
    if (!file) return
    if (!file.type.startsWith('image/')) {
      return toast.error('Please select a valid image file.')
    }
    setUploading(true)
    const filePath = `public/${initialProfile.id}`
    try {
      const { error } = await supabase.storage
        .from('avatars')
        .upload(filePath, file, { upsert: true, cacheControl: '3600' })
      if (error) throw error
      const { data: urlData } = supabase.storage
        .from('avatars')
        .getPublicUrl(filePath)
      setAvatarUrl(`${urlData?.publicUrl}?t=${Date.now()}`)
      toast.success('Avatar uploaded!')
    } catch (err: unknown) {
      const message = err instanceof Error ? err.message : 'Unknown error'
      toast.error(`Upload failed: ${message}`)
    } finally {
      setUploading(false)
    }
  }

  // Provide all 10 generic args, leaving hooks unset (undefined)
  const form = useForm<
    EditProfileFormValues,
    undefined,
    undefined,
    undefined,
    undefined,
    undefined,
    undefined,
    undefined,
    undefined,
    undefined
  >({
    defaultValues: initialValues,
    onSubmit: async ({ value }) => {
      const result = await updateProfile({ ...value, avatar_url: avatarUrl })
      if (result.error) toast.error(result.error)
      else toast.success('Profile updated!')
    },
  })

  const {
    state: { isSubmitting },
  } = form

  return (
    <form
      onSubmit={form.handleSubmit}
      noValidate
      autoComplete="off"
      className="space-y-6"
    >
      <div className="flex items-center gap-4">
        <Avatar>
          <AvatarImage src={avatarUrl} alt="Avatar" />
          <AvatarFallback>
            {form
              .getFieldValue('full_name')
              ?.split(' ')
              .map((n) => n[0])
              .join('')
              .toUpperCase() || '?'}
          </AvatarFallback>
        </Avatar>
        <input
          type="file"
          accept="image/*"
          className="block"
          onChange={handleAvatarChange}
          aria-label="Upload avatar"
          disabled={uploading}
        />
        {uploading && (
          <span className="text-muted-foreground text-sm">Uploading...</span>
        )}
      </div>

      <form.Field
        name="full_name"
        validators={{
          onChange: ({ value }) => {
            const result = EditProfileSchema.shape.full_name.safeParse(value)
            return result.success
              ? undefined
              : z.treeifyError(result.error).errors
          },
        }}
      >
        {(field) => (
          <div>
            <label htmlFor={field.name} className="mb-1 block font-medium">
              Full Name
            </label>
            <Input
              id={field.name}
              name={field.name}
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              onBlur={field.handleBlur}
              required
            />
            {field.state.meta.errors?.length && (
              <div className="text-destructive text-sm">
                {field.state.meta.errors.join(', ')}
              </div>
            )}
          </div>
        )}
      </form.Field>

      <form.Field
        name="username"
        validators={{
          onChange: ({ value }) => {
            const result = EditProfileSchema.shape.username.safeParse(value)
            return result.success
              ? undefined
              : z.treeifyError(result.error).errors
          },
        }}
      >
        {(field) => (
          <div>
            <label htmlFor={field.name} className="mb-1 block font-medium">
              Username
            </label>
            <Input
              id={field.name}
              name={field.name}
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              onBlur={field.handleBlur}
              required
            />
            {field.state.meta.errors?.length && (
              <div className="text-destructive text-sm">
                {field.state.meta.errors.join(', ')}
              </div>
            )}
          </div>
        )}
      </form.Field>

      <form.Field name="country">
        {(field) => (
          <div>
            <label htmlFor={field.name} className="mb-1 block font-medium">
              Country
            </label>
            <Input
              id={field.name}
              name={field.name}
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              onBlur={field.handleBlur}
              required
            />
            {field.state.meta.errors?.length && (
              <div className="text-destructive text-sm">
                {field.state.meta.errors.join(', ')}
              </div>
            )}
          </div>
        )}
      </form.Field>

      <form.Field name="gender">
        {(field) => (
          <div>
            <label htmlFor={field.name} className="mb-1 block font-medium">
              Gender
            </label>
            <Select
              name={field.name}
              value={field.state.value}
              onValueChange={(v) =>
                field.handleChange(() => v as 'male' | 'female' | 'other')
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="male">Male</SelectItem>
                <SelectItem value="female">Female</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
            {field.state.meta.errors?.length && (
              <div className="text-destructive text-sm">
                {field.state.meta.errors.join(', ')}
              </div>
            )}
          </div>
        )}
      </form.Field>

      <form.Field name="weight_category">
        {(field) => (
          <div>
            <label htmlFor={field.name} className="mb-1 block font-medium">
              Weight Category
            </label>
            <Select
              name={field.name}
              value={field.state.value}
              onValueChange={(v) =>
                field.handleChange(() => v as 'under_95kg' | 'over_95kg')
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="under_95kg">Under 95kg</SelectItem>
                <SelectItem value="over_95kg">Over 95kg</SelectItem>
              </SelectContent>
            </Select>
            {field.state.meta.errors?.length && (
              <div className="text-destructive text-sm">
                {field.state.meta.errors.join(', ')}
              </div>
            )}
          </div>
        )}
      </form.Field>

      <form.Field name="titles">
        {(field) => (
          <div>
            <label htmlFor={field.name} className="mb-1 block font-medium">
              Titles (comma separated)
            </label>
            <Input
              id={field.name}
              name={field.name}
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              onBlur={field.handleBlur}
            />
            {field.state.meta.errors?.length && (
              <div className="text-destructive text-sm">
                {field.state.meta.errors.join(', ')}
              </div>
            )}
          </div>
        )}
      </form.Field>

      <form.Field name="social_links">
        {(field) => (
          <div>
            <label htmlFor={field.name} className="mb-1 block font-medium">
              Social Links (JSON)
            </label>
            <Input
              id={field.name}
              name={field.name}
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              onBlur={field.handleBlur}
            />
            {field.state.meta.errors?.length && (
              <div className="text-destructive text-sm">
                {field.state.meta.errors.join(', ')}
              </div>
            )}
          </div>
        )}
      </form.Field>

      <input type="hidden" name="avatar_url" value={avatarUrl} />

      <Button
        type="submit"
        className="w-full"
        disabled={isSubmitting || uploading}
      >
        {isSubmitting || uploading ? 'Saving...' : 'Save Changes'}
      </Button>
    </form>
  )
}

export default EditProfileForm

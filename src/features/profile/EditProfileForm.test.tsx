import { render, screen, fireEvent } from '@testing-library/react'
import EditProfileForm from './EditProfileForm'
import { EditProfileFormProps } from './profileSchema'

const baseProfile: EditProfileFormProps['initialProfile'] = {
  id: 'user1',
  username: 'testuser',
  full_name: 'Test User',
  country: 'PL',
  gender: 'male',
  weight_category: 'under_95kg',
  avatar_url: 'https://example.com/avatar.png',
  titles: ['Champion', 'Master'],
  social_links: { twitter: 'https://twitter.com/test' },
}

describe('EditProfileForm', () => {
  it('renders with prefilled values', () => {
    render(<EditProfileForm initialProfile={baseProfile} />)
    expect(screen.getByLabelText(/full name/i)).toHaveValue('Test User')
    expect(screen.getByLabelText(/username/i)).toHaveValue('testuser')
    expect(screen.getByLabelText(/country/i)).toHaveValue('PL')
    expect(screen.getByLabelText(/titles/i)).toHaveValue('Champion, Master')
    expect(screen.getByLabelText(/social links/i)).toHaveValue(
      JSON.stringify({ twitter: 'https://twitter.com/test' }),
    )
  })

  it('shows validation errors on submit with empty required fields', async () => {
    render(
      <EditProfileForm
        initialProfile={{
          ...baseProfile,
          full_name: '',
          username: '',
          country: '',
        }}
      />,
    )
    fireEvent.change(screen.getByLabelText(/full name/i), {
      target: { value: '' },
    })
    fireEvent.change(screen.getByLabelText(/username/i), {
      target: { value: '' },
    })
    fireEvent.change(screen.getByLabelText(/country/i), {
      target: { value: '' },
    })
    fireEvent.click(screen.getByRole('button', { name: /save changes/i }))
    expect(await screen.findAllByText(/required/i)).toHaveLength(3)
  })
})

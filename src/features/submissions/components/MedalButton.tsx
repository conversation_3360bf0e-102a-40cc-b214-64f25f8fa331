import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import Image from 'next/image'
import { MEDAL_POINTS, type MedalType } from '../constants/medals'

interface MedalButtonProps {
  medal: MedalType
  selected: boolean
  disabled?: boolean
  onClick: (medal: MedalType) => void
  // Additional accessibility props
  'data-medal'?: string
  'aria-checked'?: boolean
  'aria-posinset'?: number
  'aria-setsize'?: number
  tabIndex?: number
}

/**
 * MedalButton component with accessibility support
 * Displays a medal option with proper ARIA attributes and keyboard navigation support
 */
export function MedalButton({
  medal,
  selected,
  disabled = false,
  onClick,
  'data-medal': dataMedal,
  'aria-checked': ariaChecked,
  'aria-posinset': ariaPosinset,
  'aria-setsize': ariaSetsize,
  tabIndex,
}: MedalButtonProps) {
  return (
    <Button
      type="button"
      aria-pressed={selected}
      aria-checked={ariaChecked ?? selected}
      aria-disabled={disabled}
      aria-posinset={ariaPosinset}
      aria-setsize={ariaSetsize}
      data-medal={dataMedal}
      tabIndex={tabIndex ?? (disabled ? -1 : 0)}
      onClick={() => !disabled && onClick(medal)}
      className={cn(
        'focus-visible:ring-primary flex h-full flex-col items-center rounded-md p-2 transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none',
        selected
          ? 'ring-primary bg-accent ring-2 ring-offset-2'
          : 'bg-muted hover:bg-accent',
        disabled && 'cursor-not-allowed opacity-50',
      )}
      variant="outline"
    >
      <div className="mb-1 h-10 w-10">
        <Image
          src={`/images/medals/${medal}.png`}
          alt={`${medal} medal`}
          width={40}
          height={40}
          className="object-contain"
        />
      </div>
      <span className="text-xs capitalize">{medal}</span>
      <span className="text-muted-foreground text-xs">
        {MEDAL_POINTS[medal]} pts
      </span>
    </Button>
  )
}

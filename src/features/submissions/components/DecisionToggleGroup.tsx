import { But<PERSON> } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { Check, X } from 'lucide-react'

interface DecisionToggleGroupProps {
  value: 'approve' | 'reject' | null
  onChange: (decision: 'approve' | 'reject') => void
  disabled?: boolean
}

export function DecisionToggleGroup({
  value,
  onChange,
  disabled = false,
}: DecisionToggleGroupProps) {
  return (
    <div className="flex flex-col gap-2">
      <span className="font-medium">Decision</span>
      <div className="grid grid-cols-2 gap-4">
        <Button
          type="button"
          aria-pressed={value === 'approve'}
          disabled={disabled}
          onClick={() => onChange('approve')}
          className={cn(
            'flex h-12 items-center justify-center rounded-md transition-colors',
            value === 'approve'
              ? 'ring-primary bg-green-600 text-white ring-2 ring-offset-2 hover:bg-green-700'
              : 'bg-muted text-muted-foreground hover:bg-accent',
            disabled && 'cursor-not-allowed opacity-50',
          )}
        >
          <Check className="mr-2 h-5 w-5" />
          Approve
        </Button>
        <Button
          type="button"
          aria-pressed={value === 'reject'}
          disabled={disabled}
          onClick={() => onChange('reject')}
          className={cn(
            'flex h-12 items-center justify-center rounded-md transition-colors',
            value === 'reject'
              ? 'ring-primary bg-red-600 text-white ring-2 ring-offset-2 hover:bg-red-700'
              : 'bg-muted text-muted-foreground hover:bg-accent',
            disabled && 'cursor-not-allowed opacity-50',
          )}
        >
          <X className="mr-2 h-5 w-5" />
          Reject
        </Button>
      </div>
    </div>
  )
}

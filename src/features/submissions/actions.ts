'use server'

import { getSupabaseRouteHandlerClient } from '@/lib/supabase/server'
import { revalidatePath } from 'next/cache'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation' // Import redirect
import type { z } from 'zod/v4'
import { submissionSchema } from './submissionSchema' // Assuming this is the Zod schema

// Define the shape of the state object returned by the action
export interface SubmissionActionState {
  success?: boolean
  message?: string
  error?: string
  submissionId?: bigint | number // Match return type of RPC
  validationErrors?: z.ZodIssue[] // More detailed validation errors
}

const VIDEO_URL_REGEX = /^https?:\/\/[^\s/$.?#].[^\s]*$/i

export async function submitPerformance(
  prevState: SubmissionActionState | undefined, // Can be undefined for initial state
  formData: FormData,
): Promise<SubmissionActionState> {
  const cookieStore = await cookies() // Await cookies() as it seems to return a Promise
  const supabase = getSupabaseRouteHandlerClient(cookieStore)

  // 1. Authentication Check
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser()

  if (authError || !user) {
    return { error: 'Authentication required. Please log in.' }
  }
  const userId = user.id

  // 2. Parse and Validate Form Data
  // Convert FormData to an object suitable for Zod parsing
  const rawFormData: Record<string, unknown> = {}
  formData.forEach((value, key) => {
    // Handle multiple values for the same key if necessary, though unlikely for this form
    rawFormData[key] = value
  })

  const validationResult = submissionSchema.safeParse(rawFormData)

  if (!validationResult.success) {
    console.error('Validation errors:', validationResult.error.issues)
    return {
      error: 'Invalid form data. Please check your inputs.',
      // Providing field-specific errors can be useful for the UI
      validationErrors: validationResult.error.issues,
    }
  }

  const { exerciseId, videoUrl, weightLifted, notes } = validationResult.data
  // submissionSchema should ideally coerce exerciseId and weightLifted to numbers.
  // If they are still strings, ensure conversion:
  const numericExerciseId =
    typeof exerciseId === 'string'
      ? Number.parseInt(exerciseId, 10)
      : exerciseId
  const numericWeightLifted =
    typeof weightLifted === 'string'
      ? Number.parseFloat(weightLifted)
      : weightLifted

  if (Number.isNaN(numericExerciseId) || Number.isNaN(numericWeightLifted)) {
    return { error: 'Exercise ID and Weight Lifted must be numbers.' }
  }

  // 3. Fetch User Profile for Role and Last Submission Time
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('role, last_submission_at')
    .eq('id', userId)
    .single()

  if (profileError || !profile) {
    console.error('Profile fetch error:', profileError)
    return { error: 'Failed to fetch user profile. Please try again.' }
  }

  // 4. Rate Limit Check (for non-admins)
  if (profile.role !== 'admin') {
    if (profile.last_submission_at) {
      const lastSubmissionDate = new Date(profile.last_submission_at)
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
      if (lastSubmissionDate > twentyFourHoursAgo) {
        return {
          error:
            'Submission limit reached. Please wait 24 hours before submitting again.',
        }
      }
    }
  }

  // 5. Video URL Validation (Basic Server-Side)
  if (!VIDEO_URL_REGEX.test(videoUrl)) {
    return { error: 'Invalid video URL format.' }
  }

  // Optional: HEAD request to check URL accessibility (with timeout)
  try {
    const response = await fetch(videoUrl, {
      method: 'HEAD',
      signal: AbortSignal.timeout(5000),
    }) // 5-second timeout
    if (!response.ok) {
      // Log the status, but decide if this should be a hard error.
      // For now, let's make it a soft warning or proceed, as HEAD can be unreliable.
      console.warn(
        `Video URL HEAD request failed with status: ${response.status} for ${videoUrl}`,
      )
      // Depending on strictness, you might return an error:
      // return { error: `Video URL seems inaccessible (status: ${response.status}). Please check it.` };
    }
  } catch (e: unknown) {
    console.warn(
      `HEAD request to ${videoUrl} failed: ${e instanceof Error ? e.message : 'Unknown error'}. Proceeding with submission...`,
    )
    // This might be a network issue or timeout. Don't block submission for this basic check if it fails.
  }

  // 6. Database Insert (using RPC function)
  const rpcParams = {
    user_id_param: userId,
    exercise_id_param: numericExerciseId,
    video_url_param: videoUrl,
    weight_lifted_param: numericWeightLifted,
    notes_param: notes || null, // Ensure notes is null if empty/undefined
  }

  const { data: newSubmissionId, error: rpcError } = await supabase.rpc(
    'create_submission_and_update_profile',
    rpcParams,
  )

  if (rpcError) {
    console.error('RPC error (create_submission_and_update_profile):', rpcError)
    return {
      error: `Failed to submit performance: ${rpcError.message}. Please try again.`,
    }
  }

  // 7. Success Handling
  revalidatePath('/account') // Revalidate path for submission history
  revalidatePath('/submissions') // Revalidate any general submissions list page
  // Don't call redirect() here if you want to return a state for the form to show a toast first.
  // The form will handle the redirect in a useEffect.
  return {
    success: true,
    message: 'Performance submitted successfully!',
    submissionId: newSubmissionId,
  }
}

'use client'

import { But<PERSON> } from '@/components/ui/button'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'

interface GoToSubmitButtonProps {
  /** ID of the exercise to submit a performance for */
  exerciseId: string
  /** Title of the exercise (used for query params) */
  exerciseTitle: string
  /** Optional CSS class name for additional styling */
  className?: string
}

/**
 * Button component that navigates to the submission page or login
 * Always renders a button, but behavior depends on authentication status
 * Uses the AuthContext to check authentication status
 */
export function GoToSubmitButton({
  exerciseId,
  exerciseTitle,
  className = '',
}: GoToSubmitButtonProps) {
  const { user, loading } = useAuth()
  const router = useRouter()

  // Handle navigation based on authentication status
  const handleClick = () => {
    if (user) {
      // User is authenticated, go to submit page
      router.push(
        `/submit?exerciseId=${exerciseId}&title=${encodeURIComponent(exerciseTitle)}`,
      )
    } else {
      // User is not authenticated, go to login
      router.push('/login')
    }
  }

  // Show loading state while checking authentication
  if (loading) {
    return (
      <Button disabled className={`w-full ${className}`} size="lg">
        Loading...
      </Button>
    )
  }

  // Always render a button with appropriate text
  return (
    <Button onClick={handleClick} className={`w-full ${className}`} size="lg">
      {user ? 'Submit Performance' : 'Login to Submit'}
    </Button>
  )
}

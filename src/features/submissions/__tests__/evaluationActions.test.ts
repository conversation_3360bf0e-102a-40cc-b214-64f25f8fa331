import { beforeEach, describe, expect, it, vi } from 'vitest'
import { evaluateSubmission } from '../evaluationActions'
import type { EvaluationActionState } from '../evaluationActions'

// Mock dependencies
vi.mock('@/lib/supabase/server', () => ({
  getSupabaseRouteHandlerClient: vi.fn(),
}))

vi.mock('next/headers', () => ({
  cookies: vi.fn(),
}))

vi.mock('next/cache', () => ({
  revalidatePath: vi.fn(),
}))

describe('evaluateSubmission', () => {
  const mockCookies = vi.fn()
  const mockSupabase = {
    auth: {
      getUser: vi.fn(),
    },
    from: vi.fn(() => ({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn(),
      update: vi.fn().mockReturnThis(),
    })),
    rpc: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()

    // Setup default mocks
    const { getSupabaseRouteHandlerClient } = require('@/lib/supabase/server')
    const { cookies } = require('next/headers')

    cookies.mockResolvedValue(mockCookies)
    getSupabaseRouteHandlerClient.mockReturnValue(mockSupabase)
  })

  describe('Authentication and Authorization', () => {
    it('should require authentication', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      })

      const result = await evaluateSubmission({
        submissionId: 'test-id',
        decision: 'approve',
        medalAwarded: 'gold',
        pointsAwarded: 6,
        feedback: 'Great job!',
      })

      expect(result.success).toBe(undefined)
      expect(result.error).toContain('signed in')
    })

    it('should handle authentication errors', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Auth failed' },
      })

      const result = await evaluateSubmission({
        submissionId: 'test-id',
        decision: 'approve',
      })

      expect(result.success).toBe(undefined)
      expect(result.error).toContain('signed in')
    })

    it('should require admin or grandmaster role', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-123' } },
        error: null,
      })

      mockSupabase
        .from()
        .select()
        .eq()
        .single.mockResolvedValue({
          data: { role: 'athlete' },
          error: null,
        })

      const result = await evaluateSubmission({
        submissionId: 'test-id',
        decision: 'approve',
        medalAwarded: 'gold',
        pointsAwarded: 6,
      })

      expect(result.success).toBe(undefined)
      expect(result.error).toContain('permission')
    })

    it('should allow admin users', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'admin-123' } },
        error: null,
      })

      // Mock profile fetch for admin
      mockSupabase
        .from()
        .select()
        .eq()
        .single.mockResolvedValueOnce({
          data: { role: 'admin' },
          error: null,
        })
        // Mock submission fetch
        .mockResolvedValueOnce({
          data: { id: 'sub-123', status: 'pending', user_id: 'athlete-123' },
          error: null,
        })

      // Mock submission update
      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: null,
        error: null,
      })

      // Mock points update
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: null,
      })

      const result = await evaluateSubmission({
        submissionId: 'sub-123',
        decision: 'approve',
        medalAwarded: 'gold',
        pointsAwarded: 6,
        feedback: 'Excellent form!',
      })

      expect(result.success).toBe(true)
    })
  })

  describe('Input Validation', () => {
    beforeEach(() => {
      // Setup authenticated admin user
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'admin-123' } },
        error: null,
      })

      mockSupabase
        .from()
        .select()
        .eq()
        .single.mockResolvedValue({
          data: { role: 'admin' },
          error: null,
        })
    })

    it('should validate required fields', async () => {
      const result = await evaluateSubmission({
        submissionId: '',
        decision: 'approve',
      })

      expect(result.success).toBe(undefined)
      expect(result.error).toContain('Invalid evaluation data')
      expect(result.validationErrors).toBeDefined()
    })

    it('should validate decision enum', async () => {
      const result = await evaluateSubmission({
        submissionId: 'valid-id',
        decision: 'invalid',
      })

      expect(result.success).toBe(undefined)
      expect(result.error).toContain('Invalid evaluation data')
    })

    it('should validate medal enum', async () => {
      const result = await evaluateSubmission({
        submissionId: 'valid-id',
        decision: 'approve',
        medalAwarded: 'invalid-medal',
        pointsAwarded: 5,
      })

      expect(result.success).toBe(undefined)
      expect(result.error).toContain('Invalid evaluation data')
    })

    it('should validate points range', async () => {
      const result = await evaluateSubmission({
        submissionId: 'valid-id',
        decision: 'approve',
        medalAwarded: 'gold',
        pointsAwarded: 150, // Exceeds max of 100
      })

      expect(result.success).toBe(undefined)
      expect(result.error).toContain('Invalid evaluation data')
    })

    it('should validate feedback length', async () => {
      const longFeedback = 'a'.repeat(501) // Exceeds 500 character limit

      const result = await evaluateSubmission({
        submissionId: 'valid-id',
        decision: 'approve',
        medalAwarded: 'gold',
        pointsAwarded: 6,
        feedback: longFeedback,
      })

      expect(result.success).toBe(undefined)
      expect(result.error).toContain('Invalid evaluation data')
    })
  })

  describe('Business Logic Validation', () => {
    beforeEach(() => {
      // Setup authenticated admin user
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'admin-123' } },
        error: null,
      })

      mockSupabase
        .from()
        .select()
        .eq()
        .single.mockResolvedValueOnce({
          data: { role: 'admin' },
          error: null,
        })
        .mockResolvedValueOnce({
          data: { id: 'sub-123', status: 'pending', user_id: 'athlete-123' },
          error: null,
        })
    })

    it('should require medal for approval', async () => {
      const result = await evaluateSubmission({
        submissionId: 'sub-123',
        decision: 'approve',
        medalAwarded: 'none',
        pointsAwarded: 6,
      })

      expect(result.success).toBe(undefined)
      expect(result.error).toContain('medal must be selected')
    })

    it('should require points for approval', async () => {
      const result = await evaluateSubmission({
        submissionId: 'sub-123',
        decision: 'approve',
        medalAwarded: 'gold',
        pointsAwarded: 0,
      })

      expect(result.success).toBe(undefined)
      expect(result.error).toContain('Points must be awarded')
    })

    it('should allow rejection without medal or points', async () => {
      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: null,
        error: null,
      })

      const result = await evaluateSubmission({
        submissionId: 'sub-123',
        decision: 'reject',
        feedback: 'Form needs improvement',
      })

      expect(result.success).toBe(true)
    })
  })

  describe('Submission Status Validation', () => {
    beforeEach(() => {
      // Setup authenticated admin user
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'admin-123' } },
        error: null,
      })

      mockSupabase
        .from()
        .select()
        .eq()
        .single.mockResolvedValueOnce({
          data: { role: 'admin' },
          error: null,
        })
    })

    it('should handle submission not found', async () => {
      mockSupabase
        .from()
        .select()
        .eq()
        .single.mockResolvedValue({
          data: null,
          error: { message: 'Not found' },
        })

      const result = await evaluateSubmission({
        submissionId: 'non-existent',
        decision: 'approve',
        medalAwarded: 'gold',
        pointsAwarded: 6,
      })

      expect(result.success).toBe(undefined)
      expect(result.error).toContain('not found')
    })

    it('should prevent evaluation of already evaluated submissions', async () => {
      mockSupabase
        .from()
        .select()
        .eq()
        .single.mockResolvedValue({
          data: { id: 'sub-123', status: 'approved', user_id: 'athlete-123' },
          error: null,
        })

      const result = await evaluateSubmission({
        submissionId: 'sub-123',
        decision: 'approve',
        medalAwarded: 'gold',
        pointsAwarded: 6,
      })

      expect(result.success).toBe(undefined)
      expect(result.error).toContain('already been evaluated')
    })
  })

  describe('Successful Evaluation', () => {
    beforeEach(() => {
      // Setup authenticated admin user
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'admin-123' } },
        error: null,
      })

      mockSupabase
        .from()
        .select()
        .eq()
        .single.mockResolvedValueOnce({
          data: { role: 'admin' },
          error: null,
        })
        .mockResolvedValueOnce({
          data: { id: 'sub-123', status: 'pending', user_id: 'athlete-123' },
          error: null,
        })

      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: null,
        error: null,
      })

      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: null,
      })
    })

    it('should successfully approve submission with medal and points', async () => {
      const result = await evaluateSubmission({
        submissionId: 'sub-123',
        decision: 'approve',
        medalAwarded: 'gold',
        pointsAwarded: 6,
        feedback: 'Excellent technique!',
      })

      expect(result.success).toBe(true)
      expect(result.message).toContain('approved')
      expect(mockSupabase.rpc).toHaveBeenCalledWith('increment_user_points', {
        user_id_param: 'athlete-123',
        points_param: 6,
      })
    })

    it('should successfully reject submission', async () => {
      const result = await evaluateSubmission({
        submissionId: 'sub-123',
        decision: 'reject',
        feedback: 'Form needs improvement',
      })

      expect(result.success).toBe(true)
      expect(result.message).toContain('rejected')
      expect(mockSupabase.rpc).not.toHaveBeenCalled()
    })

    it('should handle points update failure gracefully', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: { message: 'Points update failed' },
      })

      const result = await evaluateSubmission({
        submissionId: 'sub-123',
        decision: 'approve',
        medalAwarded: 'gold',
        pointsAwarded: 6,
      })

      expect(result.success).toBe(true)
      expect(result.message).toContain('failed to update user points')
    })
  })

  describe('Error Handling', () => {
    it('should handle unexpected errors', async () => {
      mockSupabase.auth.getUser.mockRejectedValue(new Error('Network error'))

      const result = await evaluateSubmission({
        submissionId: 'test-id',
        decision: 'approve',
      })

      expect(result.success).toBe(undefined)
      expect(result.error).toBeDefined()
    })

    it('should treat unknown input safely', async () => {
      const result = await evaluateSubmission(null)

      expect(result.success).toBe(undefined)
      expect(result.error).toContain('Invalid evaluation data')
    })

    it('should handle malformed input objects', async () => {
      const result = await evaluateSubmission({
        submissionId: 123, // Should be string
        decision: true, // Should be enum
      })

      expect(result.success).toBe(undefined)
      expect(result.error).toContain('Invalid evaluation data')
    })
  })
})

import { fireEvent, render, screen, waitFor } from '@testing-library/react'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { SubmissionForm } from '../SubmissionForm'

// Mock getSupabaseCredentials and createBrowserClient
vi.mock('@/lib/supabase/credentials', () => ({
  getSupabaseCredentials: () => ({
    supabaseUrl: 'https://test.supabase.co',
    supabaseAnonKey: 'anon-key',
  }),
}))
const mockFrom = vi.fn()
const mockSelect = vi.fn()
const mockOrder = vi.fn()
const mockThen = vi.fn()
const mockSupabase = {
  from: mockFrom,
}
mockFrom.mockReturnValue({
  select: mockSelect,
})
mockSelect.mockReturnValue({
  order: mockOrder,
})
mockOrder.mockReturnValue({
  then: mockThen,
})
vi.mock('@supabase/ssr', () => ({
  createBrowserClient: vi.fn(() => mockSupabase),
}))

beforeEach(() => {
  vi.clearAllMocks()
})

describe('SubmissionForm', () => {
  it('renders read-only exercise title and hides select when exerciseId param present', () => {
    // Mock useSearchParams
    vi.mock('next/navigation', () => ({
      useSearchParams: () => ({
        get: (key: string) =>
          key === 'exerciseId'
            ? '123e4567-e89b-12d3-a456-426614174000'
            : key === 'title'
              ? 'Bench Press'
              : null,
      }),
    }))

    render(<SubmissionForm />)
    expect(screen.getByLabelText(/exercise/i)).toHaveValue('Bench Press')
    expect(screen.queryByRole('combobox')).toBeNull()
    expect(screen.getByDisplayValue('123e4567-e89b-12d3-a456-426614174000'))
      .toBeInTheDocument
  })

  it('renders select and fetches exercises when no exerciseId param', async () => {
    vi.mock('next/navigation', () => ({
      useSearchParams: () => ({
        get: () => null,
      }),
    }))
    // Simulate fetch returning two exercises
    mockThen.mockImplementationOnce((cb) =>
      cb({
        data: [
          { id: 'id1', title: 'Bench' },
          { id: 'id2', title: 'Squat' },
        ],
        error: null,
      }),
    )

    render(<SubmissionForm />)
    expect(screen.getByLabelText(/exercise/i)).toBeInTheDocument()
    await waitFor(() => expect(screen.getByText('Bench')).toBeInTheDocument())
    expect(screen.getByText('Squat')).toBeInTheDocument()
  })

  it('shows error for invalid video URL after blur', async () => {
    vi.mock('next/navigation', () => ({
      useSearchParams: () => ({
        get: () => null,
      }),
    }))
    mockThen.mockImplementationOnce((cb) => cb({ data: [], error: null }))
    render(<SubmissionForm />)
    const videoInput = screen.getByLabelText(/video url/i)
    fireEvent.change(videoInput, { target: { value: 'not-a-url' } })
    fireEvent.blur(videoInput)
    await waitFor(() =>
      expect(screen.getByText(/valid video url/i)).toBeInTheDocument(),
    )
  })

  it('disables submit and logs data on valid submit', async () => {
    vi.mock('next/navigation', () => ({
      useSearchParams: () => ({
        get: () => null,
      }),
    }))
    mockThen.mockImplementationOnce((cb) =>
      cb({
        data: [{ id: 'id1', title: 'Bench' }],
        error: null,
      }),
    )
    const logSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
    render(<SubmissionForm />)
    // Select exercise
    fireEvent.click(screen.getByLabelText(/select exercise/i))
    fireEvent.click(screen.getByText('Bench'))
    // Fill other fields
    fireEvent.change(screen.getByLabelText(/video url/i), {
      target: { value: 'https://example.com/video.mp4' },
    })
    fireEvent.change(screen.getByLabelText(/weight lifted/i), {
      target: { value: '100' },
    })
    fireEvent.change(screen.getByLabelText(/notes/i), {
      target: { value: 'All good' },
    })
    // Submit
    fireEvent.click(screen.getByRole('button', { name: /submit/i }))
    await waitFor(() =>
      expect(logSpy).toHaveBeenCalledWith(
        'Submission:',
        expect.objectContaining({
          exerciseId: 'id1',
          videoUrl: 'https://example.com/video.mp4',
          weightLifted: 100,
          notes: 'All good',
        }),
      ),
    )
    logSpy.mockRestore()
  })

  it('shows fetch error if supabase returns error', async () => {
    vi.mock('next/navigation', () => ({
      useSearchParams: () => ({
        get: () => null,
      }),
    }))
    mockThen.mockImplementationOnce((cb) => cb({ data: null, error: 'fail' }))
    render(<SubmissionForm />)
    await waitFor(() =>
      expect(screen.getByText(/failed to load exercises/i)).toBeInTheDocument(),
    )
  })
})

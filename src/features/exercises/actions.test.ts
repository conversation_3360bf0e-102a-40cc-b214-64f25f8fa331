/// <reference types="vitest/globals" />
// src/features/exercises/actions.test.ts
// Remove explicit imports if triple-slash directive works
// import { describe, it, expect, vi, beforeEach } from 'vitest';
import { cookies } from 'next/headers'
import { revalidatePath } from 'next/cache'
import { getSupabaseRouteHandlerClient } from '@/lib/supabase/server'
import { createExercise, updateExercise, deleteExercise } from './actions'
import { type ExerciseFormData } from './ExerciseForm'

// --- Mocks --- //
vi.mock('next/headers', () => ({
  cookies: vi.fn(() => ({
    get: vi.fn(),
  })),
}))

vi.mock('next/cache', () => ({
  revalidatePath: vi.fn(),
}))

// Mock the Supabase client creator and its chain
const mockGetUser = vi.fn()
const mockInsert = vi.fn()
const mockUpdate = vi.fn()
const mockEqUpdate = vi.fn()
const mockDelete = vi.fn()
const mockEqDelete = vi.fn()
const mockFrom = vi.fn(() => ({
  insert: mockInsert,
  update: mockUpdate.mockReturnValue({ eq: mockEqUpdate }),
  delete: mockDelete.mockReturnValue({ eq: mockEqDelete }),
}))

vi.mock('@/lib/supabase/server', () => ({
  getSupabaseRouteHandlerClient: vi.fn(() => ({
    auth: {
      getUser: mockGetUser,
    },
    from: mockFrom,
  })),
}))

// Define a minimal type for flattened Zod errors
type FlattenedErrors = {
  formErrors: string[]
  fieldErrors: { [k: string]: string[] | undefined }
}

// --- Test Suite --- //
describe('Exercise Server Actions', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockGetUser.mockReset()
    mockInsert.mockReset()
    mockUpdate.mockReset()
    mockEqUpdate.mockReset()
    mockDelete.mockReset()
    mockEqDelete.mockReset()
    mockFrom.mockClear()
    // Cast to any to bypass potential vi namespace issue in mockClear
    ;(cookies as any).mockClear()
    ;(revalidatePath as any).mockClear()
    ;(getSupabaseRouteHandlerClient as any).mockClear()
  })

  // Helper to set up admin user mock
  const setupAdminUser = () => {
    mockGetUser.mockResolvedValueOnce({
      data: { user: { id: 'admin-user-id', app_metadata: { role: 'admin' } } },
      error: null,
    })
  }

  // Helper to set up non-admin user mock
  const setupNonAdminUser = () => {
    mockGetUser.mockResolvedValueOnce({
      data: {
        user: { id: 'non-admin-user-id', app_metadata: { role: 'athlete' } },
      },
      error: null,
    })
  }

  // Helper to set up unauthenticated mock
  const setupUnauthenticated = () => {
    mockGetUser.mockResolvedValueOnce({ data: { user: null }, error: null })
  }

  const validFormData: ExerciseFormData = {
    title: 'Test Exercise',
    description: 'Test Desc',
    video_tutorial_url: 'https://example.com',
    equipment_required: 'Dumbbell, Bench',
    evaluation_criteria: 'Max Reps',
    medal_thresholds: '{ "bronze": 1, "silver": 2, "gold": 3 }',
  }

  // --- createExercise --- //
  describe('createExercise', () => {
    it('should throw error if user is not authenticated', async () => {
      setupUnauthenticated()
      await expect(createExercise(validFormData)).resolves.toEqual({
        success: false,
        error: 'Unauthorized: User not authenticated.',
      })
    })

    it('should throw error if user is not admin', async () => {
      setupNonAdminUser()
      await expect(createExercise(validFormData)).resolves.toEqual({
        success: false,
        error: 'Unauthorized: Admin role required.',
      })
    })

    it('should return validation error for invalid data', async () => {
      setupAdminUser()
      const invalidData = { ...validFormData, title: '' }
      const result = await createExercise(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error).toBe('Invalid exercise data.')
        expect(result.issues).toBeDefined()
        // Use the specific cast
        const issues = result.issues as FlattenedErrors
        expect(issues.fieldErrors.title).toBeDefined()
      }
    })

    it('should return validation error for invalid JSON thresholds', async () => {
      setupAdminUser()
      const invalidData = {
        ...validFormData,
        medal_thresholds: '{ invalid json',
      }
      const result = await createExercise(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error).toBe('Invalid exercise data.')
        expect(result.issues).toBeDefined()
        // Use the specific cast
        const issues = result.issues as FlattenedErrors
        expect(issues.fieldErrors.medal_thresholds).toContain(
          'Must be valid JSON',
        )
      }
    })

    it('should return error if Supabase insert fails', async () => {
      setupAdminUser()
      mockInsert.mockResolvedValueOnce({ error: new Error('DB Insert Failed') })
      const result = await createExercise(validFormData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error).toBe('Database error: DB Insert Failed')
      }
      expect(revalidatePath).not.toHaveBeenCalled()
    })

    it('should successfully create exercise and revalidate path', async () => {
      setupAdminUser()
      mockInsert.mockResolvedValueOnce({ error: null })
      const result = await createExercise(validFormData)
      expect(result.success).toBe(true)
      expect(mockFrom).toHaveBeenCalledWith('exercises')
      expect(mockInsert).toHaveBeenCalledWith({
        title: 'Test Exercise',
        description: 'Test Desc',
        video_tutorial_url: 'https://example.com',
        evaluation_criteria: 'Max Reps',
        equipment_required: ['Dumbbell', 'Bench'],
        medal_thresholds: { bronze: 1, silver: 2, gold: 3 },
      })
      expect(revalidatePath).toHaveBeenCalledWith('/admin/exercises')
    })
  })

  // --- updateExercise --- //
  describe('updateExercise', () => {
    const exerciseId = 'exercise-123'

    it('should return error if exerciseId is missing', async () => {
      const result = await updateExercise('', validFormData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error).toBe('Exercise ID is required for update.')
      }
    })

    it('should throw error if user is not admin', async () => {
      setupNonAdminUser()
      await expect(updateExercise(exerciseId, validFormData)).resolves.toEqual({
        success: false,
        error: 'Unauthorized: Admin role required.',
      })
    })

    it('should return validation error for invalid data', async () => {
      setupAdminUser()
      const invalidData = { ...validFormData, description: '' }
      const result = await updateExercise(exerciseId, invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error).toBe('Invalid exercise data.')
        // Use the specific cast
        const issues = result.issues as FlattenedErrors
        expect(issues.fieldErrors.description).toBeDefined()
      }
    })

    it('should return error if Supabase update fails', async () => {
      setupAdminUser()
      mockEqUpdate.mockResolvedValueOnce({
        error: new Error('DB Update Failed'),
      })
      const result = await updateExercise(exerciseId, validFormData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error).toBe('Database error: DB Update Failed')
      }
      expect(revalidatePath).not.toHaveBeenCalled()
    })

    it('should successfully update exercise and revalidate path', async () => {
      setupAdminUser()
      mockEqUpdate.mockResolvedValueOnce({ error: null })
      const result = await updateExercise(exerciseId, validFormData)
      expect(result.success).toBe(true)
      expect(mockFrom).toHaveBeenCalledWith('exercises')
      expect(mockUpdate).toHaveBeenCalledWith({
        title: 'Test Exercise',
        description: 'Test Desc',
        video_tutorial_url: 'https://example.com',
        evaluation_criteria: 'Max Reps',
        equipment_required: ['Dumbbell', 'Bench'],
        medal_thresholds: { bronze: 1, silver: 2, gold: 3 },
      })
      expect(mockEqUpdate).toHaveBeenCalledWith('id', exerciseId)
      expect(revalidatePath).toHaveBeenCalledWith('/admin/exercises')
    })
  })

  // --- deleteExercise --- //
  describe('deleteExercise', () => {
    const exerciseId = 'exercise-456'

    it('should return error if exerciseId is missing', async () => {
      const result = await deleteExercise('')
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error).toBe('Exercise ID is required for deletion.')
      }
    })

    it('should throw error if user is not admin', async () => {
      setupNonAdminUser()
      await expect(deleteExercise(exerciseId)).resolves.toEqual({
        success: false,
        error: 'Unauthorized: Admin role required.',
      })
    })

    it('should return error if Supabase delete fails', async () => {
      setupAdminUser()
      mockEqDelete.mockResolvedValueOnce({
        error: new Error('DB Delete Failed'),
      })
      const result = await deleteExercise(exerciseId)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error).toBe('Database error: DB Delete Failed')
      }
      expect(revalidatePath).not.toHaveBeenCalled()
    })

    it('should successfully delete exercise and revalidate path', async () => {
      setupAdminUser()
      mockEqDelete.mockResolvedValueOnce({ error: null })
      const result = await deleteExercise(exerciseId)
      expect(result.success).toBe(true)
      expect(mockFrom).toHaveBeenCalledWith('exercises')
      expect(mockDelete).toHaveBeenCalledTimes(1)
      expect(mockEqDelete).toHaveBeenCalledWith('id', exerciseId)
      expect(revalidatePath).toHaveBeenCalledWith('/admin/exercises')
    })
  })
})

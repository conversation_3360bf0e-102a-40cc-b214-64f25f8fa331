'use client'

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  // DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import * as React from 'react'
import { ExerciseForm, type ExerciseFormData } from './ExerciseForm'
import { createExercise, updateExercise } from './actions'
import { toast } from 'sonner'

interface ExerciseModalFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit?: (data: ExerciseFormData) => Promise<void> | void
  initialData?: Partial<ExerciseFormData>
}

export const ExerciseModalForm: React.FC<ExerciseModalFormProps> = ({
  open,
  onOpenChange,
  onSubmit,
  initialData,
}) => {
  const [isSubmitting, setIsSubmitting] = React.useState(false)

  const isEditing = !!initialData?.id
  const title = isEditing ? 'Edit Exercise' : 'Create New Exercise'
  const description = isEditing
    ? 'Update the details for this exercise.'
    : 'Fill in the details to create a new exercise.'

  const formKey = React.useMemo(() => initialData?.id ?? 'new', [initialData])

  const handleFormSubmit = async (data: ExerciseFormData) => {
    setIsSubmitting(true)
    try {
      let result
      if (isEditing && initialData?.id) {
        result = await updateExercise(initialData.id, data)
      } else {
        result = await createExercise(data)
      }

      if (result.success) {
        toast.success(
          `Exercise ${isEditing ? 'updated' : 'created'} successfully!`,
        )
        onOpenChange(false)
      } else {
        let errorMessage = result.error || 'An unknown error occurred.'
        console.log('>> result.issues:', result.issues)
        if (result.issues) {
          const fieldErrors = Object.entries(result.issues.fieldErrors ?? {})
            .map(([field, errors]) => `${field}: ${errors?.join(', ')}`)
            .join('; ')
          const formErrors = (result.issues.formErrors ?? []).join(', ')
          errorMessage = `Validation failed: ${fieldErrors || formErrors || 'Check form fields.'}`
        }
        toast.error(
          `Failed to ${isEditing ? 'update' : 'create'} exercise: ${errorMessage}`,
        )
      }
    } catch (error) {
      console.error('Form submission error:', error)
      toast.error('An unexpected error occurred during submission.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        {open && (
          <ExerciseForm
            key={formKey}
            onSubmit={handleFormSubmit}
            initialData={initialData}
            className="py-4"
          />
        )}
      </DialogContent>
    </Dialog>
  )
}

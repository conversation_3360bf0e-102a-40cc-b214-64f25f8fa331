'use client'

import * as React from 'react'
import { useForm } from '@tanstack/react-form'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { cn } from '@/lib/utils'

/**
 * Defines the structure of the data collected by the exercise form.
 */
export type ExerciseFormData = {
  /** Optional ID, present when editing an existing exercise. */
  id?: string
  /** The title or name of the exercise. */
  title: string
  /** A description of how to perform the exercise. */
  description: string
  /** An optional URL pointing to a video tutorial. */
  video_tutorial_url: string
  /** A comma-separated string listing required equipment. */
  equipment_required: string
  /** Criteria used to evaluate performance (e.g., max reps, max weight). */
  evaluation_criteria: string
  /** A JSON string defining thresholds for bronze, silver, and gold medals. */
  medal_thresholds: string
}

/**
 * Props for the ExerciseForm component.
 */
interface ExerciseFormProps {
  /**
   * Callback function executed when the form is successfully submitted.
   * Receives the validated form data.
   */
  onSubmit: (data: ExerciseFormData) => Promise<void> | void
  /** Optional initial data to pre-populate the form, used for editing. */
  initialData?: Partial<ExerciseFormData>
  /** Optional CSS class name to apply to the form element. */
  className?: string
}

/**
 * A reusable form component for creating and editing exercises.
 * Uses @tanstack/react-form for state management.
 * Client-side validation is handled externally or server-side.
 *
 * @param {ExerciseFormProps} props - Component props.
 */
export const ExerciseForm: React.FC<ExerciseFormProps> = ({
  onSubmit,
  initialData = {},
  className,
}) => {
  const form = useForm({
    defaultValues: {
      title: initialData?.title ?? '',
      description: initialData?.description ?? '',
      video_tutorial_url: initialData?.video_tutorial_url ?? '',
      equipment_required: initialData?.equipment_required ?? '',
      evaluation_criteria: initialData?.evaluation_criteria ?? '',
      medal_thresholds:
        initialData?.medal_thresholds ??
        '{ "bronze": 0, "silver": 0, "gold": 0 }',
    },
    onSubmit: async ({ value }) => {
      // Ensure ID is included if present in initialData, but let onSubmit handle typed data
      const submitData = { ...value, id: initialData?.id }
      await onSubmit(submitData as ExerciseFormData) // Cast needed as value might not infer id
    },
  })

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault()
        e.stopPropagation()
        form.handleSubmit()
      }}
      className={cn('space-y-4', className)}
    >
      {/* Title Field */}
      <form.Field name="title">
        {(field) => (
          <div className="space-y-1">
            <Label htmlFor={field.name}>Title</Label>
            <Input
              id={field.name}
              name={field.name}
              value={field.state.value}
              onBlur={field.handleBlur}
              onChange={(e) => field.handleChange(e.target.value)}
              placeholder="e.g., Push-ups"
              required // Add basic HTML5 required attribute
            />
          </div>
        )}
      </form.Field>

      {/* Description Field */}
      <form.Field name="description">
        {(field) => (
          <div className="space-y-1">
            <Label htmlFor={field.name}>Description</Label>
            <Textarea
              id={field.name}
              name={field.name}
              value={field.state.value}
              onBlur={field.handleBlur}
              onChange={(e) => field.handleChange(e.target.value)}
              placeholder="Describe the exercise..."
              required
            />
          </div>
        )}
      </form.Field>

      {/* Video URL Field */}
      <form.Field name="video_tutorial_url">
        {(field) => (
          <div className="space-y-1">
            <Label htmlFor={field.name}>Video Tutorial URL (Optional)</Label>
            <Input
              id={field.name}
              name={field.name}
              value={field.state.value}
              onBlur={field.handleBlur}
              onChange={(e) => field.handleChange(e.target.value)}
              type="url"
              placeholder="https://example.com/video"
            />
          </div>
        )}
      </form.Field>

      {/* Equipment Field */}
      <form.Field name="equipment_required">
        {(field) => (
          <div className="space-y-1">
            <Label htmlFor={field.name}>
              Equipment Required (Optional, comma-separated)
            </Label>
            <Input
              id={field.name}
              name={field.name}
              value={field.state.value}
              onBlur={field.handleBlur}
              onChange={(e) => field.handleChange(e.target.value)}
              placeholder="e.g., Dumbbells, Bench"
            />
          </div>
        )}
      </form.Field>

      {/* Evaluation Criteria Field */}
      <form.Field name="evaluation_criteria">
        {(field) => (
          <div className="space-y-1">
            <Label htmlFor={field.name}>Evaluation Criteria</Label>
            <Textarea
              id={field.name}
              name={field.name}
              value={field.state.value}
              onBlur={field.handleBlur}
              onChange={(e) => field.handleChange(e.target.value)}
              placeholder="How is the exercise evaluated? (e.g., Max reps in 1 minute)"
              required
            />
          </div>
        )}
      </form.Field>

      {/* Medal Thresholds Field */}
      <form.Field name="medal_thresholds">
        {(field) => (
          <div className="space-y-1">
            <Label htmlFor={field.name}>Medal Thresholds (JSON)</Label>
            <Textarea
              id={field.name}
              name={field.name}
              value={field.state.value}
              onBlur={field.handleBlur}
              onChange={(e) => field.handleChange(e.target.value)}
              placeholder='{ "bronze": 10, "silver": 20, "gold": 30 }'
              rows={4}
              required
            />
            <p className="text-xs text-muted-foreground">
              Enter valid JSON format. E.g.,{' '}
              {'{"bronze": 10, "silver": 20, "gold": 30}'}
            </p>
          </div>
        )}
      </form.Field>

      {/* Submit Button */}
      <form.Subscribe
        selector={(state) => [state.canSubmit, state.isSubmitting]}
      >
        {([canSubmit, isSubmitting]) => (
          <Button
            type="submit"
            disabled={!canSubmit || isSubmitting}
            className="w-full"
          >
            {isSubmitting ? 'Saving...' : 'Save Exercise'}
          </Button>
        )}
      </form.Subscribe>
    </form>
  )
}

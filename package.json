{"name": "arm_pwr_arena", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:staged": "biome check --error-on-warnings --no-errors-on-unmatched --staged ./**/*.{js,ts,jsx,tsx}", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "test": "vitest run", "test:watch": "vitest", "test:supabase": "node scripts/test-supabase.mjs", "check:pwa": "node scripts/check-pwa.mjs", "test:pwa": "npm run build && npm run start & npx wait-on http://localhost:3000 && npm run check:pwa && npx lighthouse http://localhost:3000 --output-path=./lighthouse-report.html --view", "db:seed": "ts-node scripts/seed.ts"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.1.2", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.49.1", "@tanstack/react-form": "^1.3.0", "@tanstack/zod-form-adapter": "^0.42.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "lucide-react": "^0.479.0", "next": "^15.3.1", "next-pwa": "^5.6.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "sonner": "^2.0.2", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "zod": "^3.25.26"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/testing-library__jest-dom": "^5.14.9", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.2.2", "jsdom": "^26.0.0", "lighthouse": "^12.5.1", "sentences-per-line": "^0.3.0", "tailwindcss": "^4", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.2", "wait-on": "^7.2.0"}}